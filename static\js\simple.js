// HAMMAD BHAI AI Assistant - SIMPLE MODE
// Created by: <PERSON><PERSON><PERSON><PERSON>AD HAMMAD ZUBAIR
// No authentication required - direct chat access

// Simple global variables
let chatHistory = [];
let isLoading = false;

// Initialize simple mode
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 HAMMAD BHAI AI - Simple Mode Initialized');
    
    // Hide authentication modal if it exists
    hideAuthModal();
    
    // Show main chat interface
    showMainInterface();
    
    // Initialize chat
    initializeSimpleChat();
    
    // Auto-grant guest access
    grantGuestAccess();
});

function hideAuthModal() {
    const authModal = document.getElementById('authModal');
    if (authModal) {
        authModal.style.display = 'none';
    }
}

function showMainInterface() {
    const mainContainer = document.querySelector('.main-container');
    if (mainContainer) {
        mainContainer.style.display = 'flex';
    }
}

function initializeSimpleChat() {
    // Add welcome message
    addMessage('assistant', 'Hello! I\'m HAMMAD BHAI AI Assistant. How can I help you today?');
    
    // Setup chat input
    const chatInput = document.getElementById('chatInput');
    const sendButton = document.getElementById('sendButton');
    
    if (chatInput && sendButton) {
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        sendButton.addEventListener('click', sendMessage);
    }
}

function grantGuestAccess() {
    // Simple guest access without API call
    console.log('👤 Guest access granted - Simple Mode');
    
    // Update UI to show guest status
    updateUserInfo('Guest User', 'guest');
}

function updateUserInfo(username, type) {
    // Update any user info displays
    const userElements = document.querySelectorAll('.user-name');
    userElements.forEach(element => {
        element.textContent = username;
    });
    
    console.log(`✅ User: ${username} (${type})`);
}

async function sendMessage() {
    const chatInput = document.getElementById('chatInput');
    const message = chatInput.value.trim();
    
    if (!message || isLoading) return;
    
    // Clear input
    chatInput.value = '';
    
    // Add user message to chat
    addMessage('user', message);
    
    // Show loading
    isLoading = true;
    showTypingIndicator();
    
    try {
        // Send to simple API
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message
            })
        });
        
        const data = await response.json();
        
        if (response.ok && data.response) {
            // Add AI response
            addMessage('assistant', data.response);
        } else {
            addMessage('assistant', 'Sorry, I encountered an error. Please try again.');
        }
        
    } catch (error) {
        console.error('Chat error:', error);
        addMessage('assistant', 'Sorry, I\'m having trouble connecting. Please try again.');
    } finally {
        isLoading = false;
        hideTypingIndicator();
    }
}

function addMessage(role, content) {
    const chatMessages = document.getElementById('chatMessages');
    if (!chatMessages) return;
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${role}-message`;
    
    const timestamp = new Date().toLocaleTimeString();
    
    messageDiv.innerHTML = `
        <div class="message-content">
            <div class="message-text">${content}</div>
            <div class="message-time">${timestamp}</div>
        </div>
    `;
    
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    // Add to history
    chatHistory.push({
        role: role,
        content: content,
        timestamp: new Date().toISOString()
    });
}

function showTypingIndicator() {
    const chatMessages = document.getElementById('chatMessages');
    if (!chatMessages) return;
    
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message assistant-message typing-indicator';
    typingDiv.id = 'typingIndicator';
    typingDiv.innerHTML = `
        <div class="message-content">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;
    
    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function hideTypingIndicator() {
    const typingIndicator = document.getElementById('typingIndicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

// Clear chat function
async function clearChat() {
    try {
        const response = await fetch('/api/clear', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        if (response.ok) {
            // Clear UI
            const chatMessages = document.getElementById('chatMessages');
            if (chatMessages) {
                chatMessages.innerHTML = '';
            }
            
            // Clear history
            chatHistory = [];
            
            // Add welcome message
            addMessage('assistant', 'Chat cleared! How can I help you today?');
            
            console.log('✅ Chat cleared successfully');
        }
        
    } catch (error) {
        console.error('Clear chat error:', error);
    }
}

// Export functions for global access
window.sendMessage = sendMessage;
window.clearChat = clearChat;

console.log('✅ Simple mode JavaScript loaded successfully!');
