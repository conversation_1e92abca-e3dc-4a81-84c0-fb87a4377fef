// ===== HAMMAD BHAI - Ultimate AI Assistant JavaScript =====
// Created by: MUHAMMAD HAMMAD ZUBAIR

// ===== Global Variables =====
let currentUser = null;
let currentChatId = null;
let chatHistory = [];
let isTyping = false;
let currentTheme = "dark";
let sidebarCollapsed = false;
let currentModel = "gemini-2.5-flash-preview-05-20";

// ===== Initialization =====
document.addEventListener("DOMContentLoaded", function () {
  initializeApp();
});

function initializeApp() {
  // Show loading screen
  showLoadingScreen();

  // Initialize theme
  initializeTheme();

  // Load saved data
  loadSavedData();

  // Setup event listeners
  setupEventListeners();

  // Hide loading screen after 2 seconds
  setTimeout(() => {
    hideLoadingScreen();

    // Check if user is already logged in
    const savedUser = localStorage.getItem("currentUser");
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        currentUser = userData;
        document.getElementById("currentUsername").textContent =
          currentUser.username;
        hideAuthModal();
        loadChatHistory();
        showNotification(`Welcome back, ${currentUser.username}!`, "success");
      } catch (error) {
        localStorage.removeItem("currentUser");
        showAuthModal();
      }
    } else {
      showAuthModal();
    }
  }, 1000);
}

// ===== Loading Screen =====
function showLoadingScreen() {
  document.getElementById("loadingScreen").style.display = "flex";
}

function hideLoadingScreen() {
  const loadingScreen = document.getElementById("loadingScreen");
  loadingScreen.style.opacity = "0";
  setTimeout(() => {
    loadingScreen.style.display = "none";
  }, 500);
}

// ===== Authentication =====
function showAuthModal() {
  document.getElementById("authModal").style.display = "flex";
  document.getElementById("mainApp").classList.add("hidden");
}

function hideAuthModal() {
  document.getElementById("authModal").style.display = "none";
  document.getElementById("mainApp").classList.remove("hidden");
}

function switchAuthTab(tabName) {
  // Remove active class from all tabs and forms
  document
    .querySelectorAll(".auth-tab")
    .forEach((tab) => tab.classList.remove("active"));
  document
    .querySelectorAll(".auth-form")
    .forEach((form) => form.classList.remove("active"));

  // Add active class to selected tab and form
  document.querySelector(`[data-tab="${tabName}"]`).classList.add("active");
  document.getElementById(`${tabName}Form`).classList.add("active");
}

function togglePassword(inputId) {
  const input = document.getElementById(inputId);
  if (!input) {
    console.error(`Input with ID '${inputId}' not found`);
    return;
  }

  const toggleButton = input.parentElement.querySelector(".password-toggle");
  if (!toggleButton) {
    console.error(`Toggle button not found for input '${inputId}'`);
    return;
  }

  const icon = toggleButton.querySelector("i");
  if (!icon) {
    console.error(`Icon not found in toggle button for input '${inputId}'`);
    return;
  }

  if (input.type === "password") {
    input.type = "text";
    icon.classList.remove("fa-eye");
    icon.classList.add("fa-eye-slash");
    toggleButton.title = "Hide password";
    showNotification("Password visible", "info");
  } else {
    input.type = "password";
    icon.classList.remove("fa-eye-slash");
    icon.classList.add("fa-eye");
    toggleButton.title = "Show password";
    showNotification("Password hidden", "info");
  }
}

function handleLogin() {
  const username = document.getElementById("loginUsername").value;
  const password = document.getElementById("loginPassword").value;

  if (!username || !password) {
    showNotification("Please fill in all fields", "error");
    return;
  }

  // Basic validation
  if (username.length < 3) {
    showNotification("Username must be at least 3 characters", "error");
    return;
  }

  if (password.length < 6) {
    showNotification("Password must be at least 6 characters", "error");
    return;
  }

  // Simulate login with any credentials
  currentUser = {
    username: username,
    email: username.includes("@") ? username : `${username}@example.com`,
    type: "registered",
    loginTime: new Date().toISOString(),
    sessionId: generateSessionId(),
    securityLevel: "maximum",
  };

  // Save user data
  localStorage.setItem("currentUser", JSON.stringify(currentUser));

  document.getElementById("currentUsername").textContent = currentUser.username;
  hideAuthModal();
  showNotification(
    "🛡️ Login successful! Welcome " + currentUser.username,
    "success"
  );
  loadChatHistory();
}

function handleRegister() {
  const username = document.getElementById("registerUsername").value;
  const email = document.getElementById("registerEmail").value;
  const password = document.getElementById("registerPassword").value;
  const confirmPassword = document.getElementById("confirmPassword").value;

  if (!username || !email || !password || !confirmPassword) {
    showNotification("Please fill in all fields", "error");
    return;
  }

  // Basic validation
  if (username.length < 3) {
    showNotification("Username must be at least 3 characters", "error");
    return;
  }

  if (!email.includes("@") || !email.includes(".")) {
    showNotification("Please enter a valid email address", "error");
    return;
  }

  if (password.length < 6) {
    showNotification("Password must be at least 6 characters", "error");
    return;
  }

  if (password !== confirmPassword) {
    showNotification("Passwords do not match", "error");
    return;
  }

  // Create user account
  currentUser = {
    username: username,
    email: email,
    type: "registered",
    loginTime: new Date().toISOString(),
    sessionId: generateSessionId(),
    securityLevel: "maximum",
  };

  // Save user data
  localStorage.setItem("currentUser", JSON.stringify(currentUser));

  document.getElementById("currentUsername").textContent = currentUser.username;
  hideAuthModal();
  showNotification(
    "🛡️ Account created successfully! Welcome " + currentUser.username,
    "success"
  );
  loadChatHistory();
}

function enterGuestMode() {
  currentUser = {
    username: "Guest User",
    email: "<EMAIL>",
    type: "guest",
    loginTime: new Date().toISOString(),
    sessionId: generateSessionId(),
  };

  // Save guest user data to localStorage
  localStorage.setItem("currentUser", JSON.stringify(currentUser));

  document.getElementById("currentUsername").textContent = currentUser.username;
  hideAuthModal();
  showNotification("Welcome to Guest Mode!", "info");
  loadChatHistory();
}

function logout() {
  // Clear all data
  localStorage.removeItem("currentUser");
  localStorage.removeItem("chatHistory");
  sessionStorage.clear();

  currentUser = null;
  currentChatId = null;
  chatHistory = [];
  clearChatMessages();
  showAuthModal();
  showNotification("Logout completed successfully", "info");
}

// ===== Theme Management =====
function initializeTheme() {
  const savedTheme = localStorage.getItem("theme") || "dark";
  setTheme(savedTheme);
}

function toggleTheme() {
  const newTheme = currentTheme === "dark" ? "light" : "dark";
  setTheme(newTheme);
}

function setTheme(theme) {
  currentTheme = theme;
  document.documentElement.setAttribute("data-theme", theme);
  localStorage.setItem("theme", theme);

  const themeIcon = document.querySelector(".theme-toggle i");
  if (theme === "dark") {
    themeIcon.classList.replace("fa-sun", "fa-moon");
  } else {
    themeIcon.classList.replace("fa-moon", "fa-sun");
  }
}

// ===== Sidebar Management =====
function toggleSidebar() {
  const sidebar = document.getElementById("sidebar");
  const mainApp = document.getElementById("mainApp");

  // For mobile devices
  if (window.innerWidth <= 768) {
    sidebar.classList.toggle("open");
    return;
  }

  // For desktop
  sidebarCollapsed = !sidebarCollapsed;

  if (sidebarCollapsed) {
    sidebar.classList.add("collapsed");
    mainApp.classList.add("sidebar-collapsed");
  } else {
    sidebar.classList.remove("collapsed");
    mainApp.classList.remove("sidebar-collapsed");
  }

  // Save sidebar state
  localStorage.setItem("sidebarCollapsed", sidebarCollapsed);
}

function toggleUserMenu() {
  const userProfile = document.querySelector(".user-profile");
  const userMenu = document.getElementById("userMenu");

  userProfile.classList.toggle("open");
  userMenu.classList.toggle("hidden");
}

// ===== Chat Management =====
function startNewChat() {
  currentChatId = generateChatId();
  clearChatMessages();
  showWelcomeScreen();
  updateChatTitle("HAMMAD BHAI", "Ultimate AI Assistant");

  // Add to chat history
  const newChat = {
    id: currentChatId,
    title: "New Chat",
    preview: "Start a conversation...",
    timestamp: new Date(),
    messages: [],
  };

  chatHistory.unshift(newChat);
  updateChatHistoryUI();
  saveChatHistory();
}

function updateChatHistoryUI() {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  const weekAgo = new Date(today);
  weekAgo.setDate(weekAgo.getDate() - 7);

  // Clear all chat lists
  document.getElementById("todayChats").innerHTML = "";
  document.getElementById("yesterdayChats").innerHTML = "";
  document.getElementById("weekChats").innerHTML = "";
  document.getElementById("olderChats").innerHTML = "";

  chatHistory.forEach((chat) => {
    const chatDate = new Date(chat.timestamp);
    const chatElement = createChatHistoryItem(chat);

    if (isSameDay(chatDate, today)) {
      document.getElementById("todayChats").appendChild(chatElement);
    } else if (isSameDay(chatDate, yesterday)) {
      document.getElementById("yesterdayChats").appendChild(chatElement);
    } else if (chatDate > weekAgo) {
      document.getElementById("weekChats").appendChild(chatElement);
    } else {
      document.getElementById("olderChats").appendChild(chatElement);
    }
  });
}

function createChatHistoryItem(chat) {
  const chatItem = document.createElement("div");
  chatItem.className = "chat-item";
  chatItem.dataset.chatId = chat.id;

  if (chat.id === currentChatId) {
    chatItem.classList.add("active");
  }

  chatItem.innerHTML = `
        <div class="chat-item-icon">
            <i class="fas fa-comment"></i>
        </div>
        <div class="chat-item-content">
            <div class="chat-item-title">${chat.title}</div>
            <div class="chat-item-preview">${chat.preview}</div>
        </div>
        <div class="chat-item-actions">
            <button class="chat-action-btn" onclick="editChatTitle('${chat.id}')" title="Edit">
                <i class="fas fa-edit"></i>
            </button>
            <button class="chat-action-btn" onclick="deleteChat('${chat.id}')" title="Delete">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;

  chatItem.addEventListener("click", (e) => {
    if (!e.target.closest(".chat-item-actions")) {
      loadChat(chat.id);
    }
  });

  return chatItem;
}

function isSameDay(date1, date2) {
  return (
    date1.getDate() === date2.getDate() &&
    date1.getMonth() === date2.getMonth() &&
    date1.getFullYear() === date2.getFullYear()
  );
}

function updateActiveChatInHistory(chatId) {
  document.querySelectorAll(".chat-item").forEach((item) => {
    item.classList.remove("active");
  });

  const activeItem = document.querySelector(`[data-chat-id="${chatId}"]`);
  if (activeItem) {
    activeItem.classList.add("active");
  }
}

function editChatTitle(chatId) {
  const chat = chatHistory.find((c) => c.id === chatId);
  if (!chat) return;

  const newTitle = prompt("Enter new chat title:", chat.title);
  if (newTitle && newTitle.trim()) {
    chat.title = newTitle.trim();
    updateChatHistoryUI();
    saveChatHistory();

    if (chatId === currentChatId) {
      updateChatTitle(chat.title, `${chat.messages.length} messages`);
    }

    showNotification("Chat title updated", "success");
  }
}

function loadChat(chatId) {
  const chat = chatHistory.find((c) => c.id === chatId);
  if (!chat) return;

  currentChatId = chatId;
  clearChatMessages();
  hideWelcomeScreen();

  // Load messages
  chat.messages.forEach((message) => {
    addMessageToUI(message.content, message.isUser, false);
  });

  updateChatTitle(chat.title, `${chat.messages.length} messages`);
  updateActiveChatInHistory(chatId);
}

function deleteChat(chatId) {
  if (confirm("Are you sure you want to delete this chat?")) {
    chatHistory = chatHistory.filter((c) => c.id !== chatId);

    if (currentChatId === chatId) {
      startNewChat();
    }

    updateChatHistoryUI();
    saveChatHistory();
    showNotification("Chat deleted", "success");
  }
}

function deleteAllChats() {
  if (
    confirm(
      "Are you sure you want to delete all chats? This action cannot be undone."
    )
  ) {
    chatHistory = [];
    startNewChat();
    updateChatHistoryUI();
    saveChatHistory();
    showNotification("All chats deleted", "success");
  }
}

function exportChats() {
  // Show export options modal
  showExportModal();
}

function showExportModal() {
  const modal = document.createElement("div");
  modal.className = "export-modal";
  modal.innerHTML = `
    <div class="export-modal-content">
      <div class="export-modal-header">
        <h3><i class="fas fa-download"></i> Export Chats</h3>
        <button class="close-btn" onclick="this.closest('.export-modal').remove()">&times;</button>
      </div>
      <div class="export-modal-body">
        <p>Choose export format:</p>
        <div class="export-options">
          <button class="export-option-btn" onclick="exportAsJSON()">
            <i class="fas fa-file-code"></i>
            <span>JSON Format</span>
            <small>Machine readable format</small>
          </button>
          <button class="export-option-btn" onclick="exportAsPDF()">
            <i class="fas fa-file-pdf"></i>
            <span>PDF Document</span>
            <small>Human readable format</small>
          </button>
          <button class="export-option-btn" onclick="exportAsText()">
            <i class="fas fa-file-alt"></i>
            <span>Text File</span>
            <small>Plain text format</small>
          </button>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // Close modal when clicking outside
  modal.addEventListener("click", (e) => {
    if (e.target === modal) {
      modal.remove();
    }
  });
}

function exportAsJSON() {
  const dataStr = JSON.stringify(chatHistory, null, 2);
  const dataBlob = new Blob([dataStr], { type: "application/json" });

  const link = document.createElement("a");
  link.href = URL.createObjectURL(dataBlob);
  link.download = `hammad-bhai-chats-${
    new Date().toISOString().split("T")[0]
  }.json`;
  link.click();

  document.querySelector(".export-modal")?.remove();
  showNotification("Chats exported as JSON", "success");
}

function exportAsPDF() {
  // Create PDF content
  let pdfContent = `
HAMMAD BHAI - Chat History Export
Generated on: ${new Date().toLocaleString()}
Created by: MUHAMMAD HAMMAD ZUBAIR

========================================

`;

  chatHistory.forEach((chat, index) => {
    pdfContent += `\nChat ${index + 1}: ${chat.title}\n`;
    pdfContent += `Date: ${new Date(chat.timestamp).toLocaleString()}\n`;
    pdfContent += `Messages: ${chat.messages.length}\n`;
    pdfContent += `${"=".repeat(50)}\n\n`;

    chat.messages.forEach((message, msgIndex) => {
      const sender = message.isUser
        ? currentUser?.username || "User"
        : "HAMMAD BHAI";
      pdfContent += `${msgIndex + 1}. ${sender}:\n${message.content}\n\n`;
    });

    pdfContent += `\n${"=".repeat(50)}\n\n`;
  });

  // Create and download PDF-like text file
  const dataBlob = new Blob([pdfContent], { type: "text/plain" });
  const link = document.createElement("a");
  link.href = URL.createObjectURL(dataBlob);
  link.download = `hammad-bhai-chats-${
    new Date().toISOString().split("T")[0]
  }.txt`;
  link.click();

  document.querySelector(".export-modal")?.remove();
  showNotification("Chats exported as PDF-Text", "success");
}

function exportAsText() {
  let textContent = `HAMMAD BHAI - Chat History\nExported on: ${new Date().toLocaleString()}\n\n`;

  chatHistory.forEach((chat, index) => {
    textContent += `=== Chat ${index + 1}: ${chat.title} ===\n`;
    chat.messages.forEach((message) => {
      const sender = message.isUser ? "You" : "HAMMAD BHAI";
      textContent += `${sender}: ${message.content}\n`;
    });
    textContent += "\n";
  });

  const dataBlob = new Blob([textContent], { type: "text/plain" });
  const link = document.createElement("a");
  link.href = URL.createObjectURL(dataBlob);
  link.download = `hammad-bhai-chats-${
    new Date().toISOString().split("T")[0]
  }.txt`;
  link.click();

  document.querySelector(".export-modal")?.remove();
  showNotification("Chats exported as Text", "success");
}

function searchChats() {
  const query = document.getElementById("chatSearch").value.toLowerCase();
  const chatItems = document.querySelectorAll(".chat-item");

  chatItems.forEach((item) => {
    const title = item
      .querySelector(".chat-item-title")
      .textContent.toLowerCase();
    const preview = item
      .querySelector(".chat-item-preview")
      .textContent.toLowerCase();

    if (title.includes(query) || preview.includes(query)) {
      item.style.display = "flex";
    } else {
      item.style.display = "none";
    }
  });
}

// ===== Message Handling =====
function sendMessage() {
  const input = document.getElementById("messageInput");
  const message = input.value.trim();

  if (!message || isTyping) return;

  // Basic input validation
  if (message.length > 4000) {
    showNotification(
      "Message too long. Please keep it under 4000 characters.",
      "error"
    );
    return;
  }

  // Add user message
  addMessageToUI(message, true);
  input.value = "";
  autoResize(input);
  updateSendButton();

  // Hide welcome screen
  hideWelcomeScreen();

  // Save message to current chat
  saveMessageToChat(message, true);

  // Send to AI
  sendToAI(message);
}

function sendQuickMessage(message) {
  document.getElementById("messageInput").value = message;
  sendMessage();
}

async function sendToAI(message) {
  isTyping = true;
  showTypingIndicator();

  try {
    // Check if online
    if (!navigator.onLine) {
      throw new Error("No internet connection. Please check your network.");
    }

    const response = await fetch("/api/chat", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        message: message,
        model: currentModel,
      }),
    });

    // Better error handling
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorMessage =
        errorData.error || `HTTP ${response.status}: ${response.statusText}`;
      throw new Error(errorMessage);
    }

    const data = await response.json();

    if (data.response) {
      hideTypingIndicator();
      addMessageToUI(data.response, false);
      saveMessageToChat(data.response, false);
      updateChatPreview(message, data.response);
    } else {
      throw new Error(data.error || "Failed to get response from AI");
    }
  } catch (error) {
    hideTypingIndicator();

    // More specific error messages
    let errorMessage = "Sorry, I encountered an error. Please try again.";

    if (error.message.includes("network") || error.message.includes("fetch")) {
      errorMessage = "🌐 Network error. Please check your internet connection.";
    } else if (error.message.includes("timeout")) {
      errorMessage = "⏱️ Request timed out. Please try again.";
    } else if (error.message.includes("API")) {
      errorMessage =
        "🔧 API service temporarily unavailable. Please try again later.";
    }

    addMessageToUI(errorMessage, false);
    showNotification("Error: " + error.message, "error");

    // Log error for debugging
    console.error("AI API Error:", error);
  }

  isTyping = false;
}

function addMessageToUI(content, isUser, animate = true) {
  const messagesContainer = document.getElementById("chatMessages");
  const messageDiv = document.createElement("div");
  messageDiv.className = `message ${isUser ? "user-message" : "ai-message"}`;

  if (animate) {
    messageDiv.classList.add("animate__animated", "animate__fadeInUp");
  }

  messageDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas ${isUser ? "fa-user" : "fa-robot"}"></i>
        </div>
        <div class="message-content">
            <div class="message-header">
                <span class="message-sender">${
                  isUser ? currentUser?.username || "You" : "HAMMAD BHAI"
                }</span>
                <span class="message-time">${new Date().toLocaleTimeString()}</span>
            </div>
            <div class="message-text">${formatMessage(content)}</div>
            <div class="message-actions">
                <button class="message-action-btn" onclick="copyMessage(this)" title="Copy">
                    <i class="fas fa-copy"></i>
                </button>
                ${
                  isUser
                    ? `
                    <button class="message-action-btn" onclick="editMessage(this)" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="message-action-btn" onclick="deleteMessage(this)" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>
                `
                    : `
                    <button class="message-action-btn" onclick="regenerateResponse()" title="Regenerate">
                        <i class="fas fa-redo"></i>
                    </button>
                    <button class="message-action-btn" onclick="likeMessage(this)" title="Like">
                        <i class="fas fa-thumbs-up"></i>
                    </button>
                    <button class="message-action-btn" onclick="speakMessage(this)" title="Speak">
                        <i class="fas fa-volume-up"></i>
                    </button>
                `
                }
            </div>
        </div>
    `;

  messagesContainer.appendChild(messageDiv);
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function formatMessage(content) {
  // Basic markdown-like formatting
  return content
    .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
    .replace(/\*(.*?)\*/g, "<em>$1</em>")
    .replace(/`(.*?)`/g, "<code>$1</code>")
    .replace(/\n/g, "<br>");
}

function showTypingIndicator() {
  const messagesContainer = document.getElementById("chatMessages");
  const typingDiv = document.createElement("div");
  typingDiv.className = "message ai-message typing-indicator";
  typingDiv.id = "typingIndicator";

  typingDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;

  messagesContainer.appendChild(typingDiv);
  messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function hideTypingIndicator() {
  const typingIndicator = document.getElementById("typingIndicator");
  if (typingIndicator) {
    typingIndicator.remove();
  }
}

// ===== Input Handling =====
function handleKeyDown(event) {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
}

function autoResize(textarea) {
  textarea.style.height = "auto";
  textarea.style.height = Math.min(textarea.scrollHeight, 120) + "px";

  // Update character count
  const charCount = textarea.value.length;
  document.getElementById("charCount").textContent = charCount;

  // Update send button
  updateSendButton();
}

function updateSendButton() {
  const input = document.getElementById("messageInput");
  const sendBtn = document.getElementById("sendBtn");

  if (input.value.trim() && !isTyping) {
    sendBtn.disabled = false;
    sendBtn.classList.add("active");
  } else {
    sendBtn.disabled = true;
    sendBtn.classList.remove("active");
  }
}

// ===== Utility Functions =====
function generateChatId() {
  return (
    "chat_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11)
  );
}

function generateSessionId() {
  return (
    "session_" + Date.now() + "_" + Math.random().toString(36).substring(2, 11)
  );
}

function showNotification(message, type = "info") {
  // Create notification element
  const notification = document.createElement("div");
  notification.className = `notification notification-${type} animate__animated animate__fadeInRight`;
  notification.innerHTML = `
        <i class="fas ${getNotificationIcon(type)}"></i>
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

  // Add to page
  document.body.appendChild(notification);

  // Auto remove after 5 seconds
  setTimeout(() => {
    if (notification.parentElement) {
      notification.classList.replace(
        "animate__fadeInRight",
        "animate__fadeOutRight"
      );
      setTimeout(() => notification.remove(), 300);
    }
  }, 5000);
}

function getNotificationIcon(type) {
  switch (type) {
    case "success":
      return "fa-check-circle";
    case "error":
      return "fa-exclamation-circle";
    case "warning":
      return "fa-exclamation-triangle";
    default:
      return "fa-info-circle";
  }
}

function showWelcomeScreen() {
  document.getElementById("welcomeScreen").classList.remove("hidden");
  document.getElementById("chatMessages").classList.add("hidden");
}

function hideWelcomeScreen() {
  document.getElementById("welcomeScreen").classList.add("hidden");
  document.getElementById("chatMessages").classList.remove("hidden");
}

function clearChatMessages() {
  document.getElementById("chatMessages").innerHTML = "";
}

function updateChatTitle(title, subtitle) {
  document.getElementById("currentChatTitle").textContent = title;
  document.getElementById("chatSubtitle").textContent = subtitle;
}

// ===== Event Listeners Setup =====
function setupEventListeners() {
  // Auth tab switching
  document.querySelectorAll(".auth-tab").forEach((tab) => {
    tab.addEventListener("click", () => switchAuthTab(tab.dataset.tab));
  });

  // Input events
  const messageInput = document.getElementById("messageInput");
  messageInput.addEventListener("input", () => autoResize(messageInput));
  messageInput.addEventListener("keydown", handleKeyDown);

  // Model switching
  document.getElementById("aiModel").addEventListener("change", (e) => {
    currentModel = e.target.value;
    showNotification(
      `Switched to ${e.target.selectedOptions[0].text}`,
      "success"
    );
  });

  // Sidebar toggle for mobile
  if (window.innerWidth <= 768) {
    document.addEventListener("click", (e) => {
      const sidebar = document.getElementById("sidebar");
      if (
        !sidebar.contains(e.target) &&
        !e.target.closest(".mobile-menu-btn")
      ) {
        sidebar.classList.remove("open");
      }
    });
  }
}

// ===== Data Persistence =====
function loadSavedData() {
  // Load theme
  const savedTheme = localStorage.getItem("theme");
  if (savedTheme) {
    setTheme(savedTheme);
  }

  // Load sidebar state
  const savedSidebarState = localStorage.getItem("sidebarCollapsed");
  if (savedSidebarState === "true") {
    sidebarCollapsed = true;
    const sidebar = document.getElementById("sidebar");
    const mainApp = document.getElementById("mainApp");
    if (sidebar && mainApp) {
      sidebar.classList.add("collapsed");
      mainApp.classList.add("sidebar-collapsed");
    }
  }

  // Load chat history
  const savedChats = localStorage.getItem("chatHistory");
  if (savedChats) {
    chatHistory = JSON.parse(savedChats);
  }
}

function loadChatHistory() {
  updateChatHistoryUI();
  if (chatHistory.length === 0) {
    startNewChat();
  } else {
    // Load the most recent chat
    loadChat(chatHistory[0].id);
  }
}

function saveChatHistory() {
  localStorage.setItem("chatHistory", JSON.stringify(chatHistory));
}

function saveMessageToChat(content, isUser) {
  if (!currentChatId) return;

  const chat = chatHistory.find((c) => c.id === currentChatId);
  if (chat) {
    chat.messages.push({
      content: content,
      isUser: isUser,
      timestamp: new Date(),
    });

    // Update chat title if it's the first message
    if (chat.messages.length === 1 && isUser) {
      chat.title =
        content.substring(0, 50) + (content.length > 50 ? "..." : "");
    }

    // Update chat preview
    if (!isUser) {
      chat.preview =
        content.substring(0, 100) + (content.length > 100 ? "..." : "");
    }

    saveChatHistory();
    updateChatHistoryUI();
  }
}

function updateChatPreview(userMessage, aiResponse) {
  const chat = chatHistory.find((c) => c.id === currentChatId);
  if (chat) {
    chat.preview =
      aiResponse.substring(0, 100) + (aiResponse.length > 100 ? "..." : "");
    updateChatHistoryUI();
  }
}

// ===== Additional Features =====
function switchModel() {
  const select = document.getElementById("aiModel");
  currentModel = select.value;
  showNotification(`Switched to ${select.selectedOptions[0].text}`, "success");
}

function editMessage(button) {
  const messageDiv = button.closest(".message");
  const messageText = messageDiv.querySelector(".message-text");
  const currentText = messageText.textContent;

  // Create edit input
  const editInput = document.createElement("textarea");
  editInput.value = currentText;
  editInput.className = "edit-message-input";
  editInput.style.cssText = `
    width: 100%;
    min-height: 60px;
    padding: 10px;
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: inherit;
    font-size: 14px;
    resize: vertical;
  `;

  // Create action buttons
  const actionDiv = document.createElement("div");
  actionDiv.style.cssText = "margin-top: 8px; display: flex; gap: 8px;";
  actionDiv.innerHTML = `
    <button onclick="saveEditedMessage(this, '${
      messageDiv.dataset.messageId || Date.now()
    }')"
            style="padding: 6px 12px; background: var(--primary-color); color: white; border: none; border-radius: 4px; cursor: pointer;">
      <i class="fas fa-save"></i> Save
    </button>
    <button onclick="cancelEdit(this)"
            style="padding: 6px 12px; background: var(--bg-tertiary); color: var(--text-primary); border: none; border-radius: 4px; cursor: pointer;">
      <i class="fas fa-times"></i> Cancel
    </button>
  `;

  // Store original content
  messageText.dataset.originalText = currentText;

  // Replace content with edit interface
  messageText.innerHTML = "";
  messageText.appendChild(editInput);
  messageText.appendChild(actionDiv);

  editInput.focus();
  editInput.select();
}

function saveEditedMessage(button, messageId) {
  const messageDiv = button.closest(".message");
  const messageText = messageDiv.querySelector(".message-text");
  const editInput = messageText.querySelector(".edit-message-input");
  const newText = editInput.value.trim();

  if (newText) {
    messageText.innerHTML = formatMessage(newText);

    // Update in chat history
    const chat = chatHistory.find((c) => c.id === currentChatId);
    if (chat) {
      const messageIndex = Array.from(
        document.querySelectorAll(".message")
      ).indexOf(messageDiv);
      if (chat.messages[messageIndex]) {
        chat.messages[messageIndex].content = newText;
        saveChatHistory();
      }
    }

    showNotification("Message updated successfully", "success");
  } else {
    cancelEdit(button);
  }
}

function cancelEdit(button) {
  const messageDiv = button.closest(".message");
  const messageText = messageDiv.querySelector(".message-text");
  const originalText = messageText.dataset.originalText;

  messageText.innerHTML = formatMessage(originalText);
  delete messageText.dataset.originalText;
}

function deleteMessage(button) {
  if (confirm("Are you sure you want to delete this message?")) {
    const messageDiv = button.closest(".message");
    const messageIndex = Array.from(
      document.querySelectorAll(".message")
    ).indexOf(messageDiv);

    // Remove from chat history
    const chat = chatHistory.find((c) => c.id === currentChatId);
    if (chat && chat.messages[messageIndex]) {
      chat.messages.splice(messageIndex, 1);
      saveChatHistory();
    }

    // Remove from UI
    messageDiv.remove();
    showNotification("Message deleted", "success");
  }
}

function speakMessage(button) {
  const messageText = button
    .closest(".message-content")
    .querySelector(".message-text").textContent;

  if ("speechSynthesis" in window) {
    // Stop any ongoing speech
    speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(messageText);
    utterance.rate = 0.8;
    utterance.pitch = 1;
    utterance.volume = 1;

    // Change button to stop icon
    const icon = button.querySelector("i");
    icon.classList.replace("fa-volume-up", "fa-stop");
    button.title = "Stop Speaking";
    button.onclick = () => stopSpeaking(button);

    utterance.onend = () => {
      icon.classList.replace("fa-stop", "fa-volume-up");
      button.title = "Speak";
      button.onclick = () => speakMessage(button);
    };

    speechSynthesis.speak(utterance);
    showNotification("Speaking message...", "info");
  } else {
    showNotification(
      "Speech synthesis not supported in your browser",
      "warning"
    );
  }
}

function stopSpeaking(button) {
  speechSynthesis.cancel();
  const icon = button.querySelector("i");
  icon.classList.replace("fa-stop", "fa-volume-up");
  button.title = "Speak";
  button.onclick = () => speakMessage(button);
  showNotification("Speech stopped", "info");
}

function showSettings() {
  showSettingsModal();
}

function showSettingsModal() {
  const modal = document.createElement("div");
  modal.className = "settings-modal";
  modal.innerHTML = `
    <div class="settings-modal-content">
      <div class="settings-modal-header">
        <h3><i class="fas fa-cog"></i> Settings</h3>
        <button class="close-btn" onclick="this.closest('.settings-modal').remove()">&times;</button>
      </div>
      <div class="settings-modal-body">
        <div class="setting-group">
          <h4><i class="fas fa-palette"></i> Appearance</h4>
          <div class="setting-item">
            <label>Theme</label>
            <select id="themeSelect" onchange="changeTheme(this.value)">
              <option value="dark" ${
                currentTheme === "dark" ? "selected" : ""
              }>Dark</option>
              <option value="light" ${
                currentTheme === "light" ? "selected" : ""
              }>Light</option>
            </select>
          </div>
        </div>

        <div class="setting-group">
          <h4><i class="fas fa-robot"></i> AI Settings</h4>
          <div class="setting-item">
            <label>Default Model</label>
            <select id="defaultModelSelect" onchange="setDefaultModel(this.value)">
              <option value="gemini-2.5-flash-preview-05-20">Gemini 2.5 Flash (Most Powerful)</option>
              <option value="gemini-2.0-flash-exp">Gemini 2.0 Experimental</option>
              <option value="gemini-2.0-flash">Gemini 2.0 Flash</option>
              <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
              <option value="gemini-pro">Gemini Pro</option>
            </select>
          </div>
        </div>

        <div class="setting-group">
          <h4><i class="fas fa-keyboard"></i> Shortcuts</h4>
          <div class="shortcuts-list">
            <div class="shortcut-item">
              <span>New Chat</span>
              <kbd>Ctrl + N</kbd>
            </div>
            <div class="shortcut-item">
              <span>Search Chats</span>
              <kbd>Ctrl + K</kbd>
            </div>
            <div class="shortcut-item">
              <span>Send Message</span>
              <kbd>Enter</kbd>
            </div>
            <div class="shortcut-item">
              <span>New Line</span>
              <kbd>Shift + Enter</kbd>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // Close modal when clicking outside
  modal.addEventListener("click", (e) => {
    if (e.target === modal) {
      modal.remove();
    }
  });
}

function changeTheme(theme) {
  setTheme(theme);
}

function setDefaultModel(model) {
  currentModel = model;
  document.getElementById("aiModel").value = model;
  localStorage.setItem("defaultModel", model);
  showNotification(`Default model set to ${model}`, "success");
}

function showProfile() {
  showProfileModal();
}

function showProfileModal() {
  const modal = document.createElement("div");
  modal.className = "profile-modal";
  modal.innerHTML = `
    <div class="profile-modal-content">
      <div class="profile-modal-header">
        <h3><i class="fas fa-user"></i> Profile</h3>
        <button class="close-btn" onclick="this.closest('.profile-modal').remove()">&times;</button>
      </div>
      <div class="profile-modal-body">
        <div class="profile-info">
          <div class="profile-avatar">
            <i class="fas fa-user"></i>
          </div>
          <div class="profile-details">
            <h4>${currentUser?.username || "Guest User"}</h4>
            <p>${currentUser?.email || "<EMAIL>"}</p>
            <span class="user-type">${currentUser?.type || "guest"}</span>
          </div>
        </div>

        <div class="profile-stats">
          <div class="stat-item">
            <i class="fas fa-comments"></i>
            <span>Total Chats</span>
            <strong>${chatHistory.length}</strong>
          </div>
          <div class="stat-item">
            <i class="fas fa-message"></i>
            <span>Total Messages</span>
            <strong>${chatHistory.reduce(
              (total, chat) => total + chat.messages.length,
              0
            )}</strong>
          </div>
          <div class="stat-item">
            <i class="fas fa-calendar"></i>
            <span>Member Since</span>
            <strong>Today</strong>
          </div>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // Close modal when clicking outside
  modal.addEventListener("click", (e) => {
    if (e.target === modal) {
      modal.remove();
    }
  });
}

function showHelp() {
  showHelpModal();
}

function showHelpModal() {
  const modal = document.createElement("div");
  modal.className = "help-modal";
  modal.innerHTML = `
    <div class="help-modal-content">
      <div class="help-modal-header">
        <h3><i class="fas fa-question-circle"></i> Help & Support</h3>
        <button class="close-btn" onclick="this.closest('.help-modal').remove()">&times;</button>
      </div>
      <div class="help-modal-body">
        <div class="help-section">
          <h4><i class="fas fa-rocket"></i> Getting Started</h4>
          <ul>
            <li>Type your message in the input box and press Enter</li>
            <li>Use Shift+Enter for new lines</li>
            <li>Click "New Chat" to start a fresh conversation</li>
            <li>Your chats are automatically saved</li>
          </ul>
        </div>

        <div class="help-section">
          <h4><i class="fas fa-keyboard"></i> Keyboard Shortcuts</h4>
          <ul>
            <li><kbd>Ctrl + N</kbd> - New Chat</li>
            <li><kbd>Ctrl + K</kbd> - Search Chats</li>
            <li><kbd>Enter</kbd> - Send Message</li>
            <li><kbd>Shift + Enter</kbd> - New Line</li>
            <li><kbd>Escape</kbd> - Close Modals</li>
          </ul>
        </div>

        <div class="help-section">
          <h4><i class="fas fa-features"></i> Features</h4>
          <ul>
            <li>Edit and delete your messages</li>
            <li>Copy AI responses</li>
            <li>Regenerate AI responses</li>
            <li>Text-to-speech for AI messages</li>
            <li>Export chats in multiple formats</li>
            <li>Dark/Light theme toggle</li>
          </ul>
        </div>

        <div class="help-section">
          <h4><i class="fas fa-info-circle"></i> About</h4>
          <p>HAMMAD BHAI is an advanced AI assistant created by <strong>MUHAMMAD HAMMAD ZUBAIR</strong>.
          It features multiple AI models, real-time responses, and a professional interface designed for the best user experience.</p>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // Close modal when clicking outside
  modal.addEventListener("click", (e) => {
    if (e.target === modal) {
      modal.remove();
    }
  });
}

function showAttachmentMenu() {
  showAttachmentModal();
}

function showAttachmentModal() {
  const modal = document.createElement("div");
  modal.className = "attachment-modal";
  modal.innerHTML = `
    <div class="attachment-modal-content">
      <div class="attachment-modal-header">
        <h3><i class="fas fa-paperclip"></i> Attach File</h3>
        <button class="close-btn" onclick="this.closest('.attachment-modal').remove()">&times;</button>
      </div>
      <div class="attachment-modal-body">
        <div class="attachment-options">
          <div class="attachment-option" onclick="selectFile('image')">
            <i class="fas fa-image"></i>
            <span>Image</span>
            <small>JPG, PNG, GIF</small>
          </div>
          <div class="attachment-option" onclick="selectFile('document')">
            <i class="fas fa-file-alt"></i>
            <span>Document</span>
            <small>PDF, DOC, TXT</small>
          </div>
          <div class="attachment-option" onclick="selectFile('code')">
            <i class="fas fa-code"></i>
            <span>Code</span>
            <small>JS, PY, HTML, CSS</small>
          </div>
        </div>
        <input type="file" id="fileInput" style="display: none;" onchange="handleFileSelect(this)">
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // Close modal when clicking outside
  modal.addEventListener("click", (e) => {
    if (e.target === modal) {
      modal.remove();
    }
  });
}

function selectFile(type) {
  const fileInput = document.getElementById("fileInput");

  switch (type) {
    case "image":
      fileInput.accept = ".jpg,.jpeg,.png,.gif,.webp";
      break;
    case "document":
      fileInput.accept = ".pdf,.doc,.docx,.txt,.rtf";
      break;
    case "code":
      fileInput.accept = ".js,.py,.html,.css,.json,.xml,.sql";
      break;
  }

  fileInput.click();
}

function handleFileSelect(input) {
  const file = input.files[0];
  if (file) {
    if (file.size > 10 * 1024 * 1024) {
      // 10MB limit
      showNotification("File size too large. Maximum 10MB allowed.", "error");
      return;
    }

    const reader = new FileReader();
    reader.onload = function (e) {
      const content = e.target.result;
      const messageInput = document.getElementById("messageInput");
      messageInput.value += `\n\n[File: ${file.name}]\n${content}`;
      autoResize(messageInput);

      document.querySelector(".attachment-modal").remove();
      showNotification(`File "${file.name}" attached successfully`, "success");
    };

    if (
      file.type.startsWith("text/") ||
      file.name.endsWith(".txt") ||
      file.name.endsWith(".js") ||
      file.name.endsWith(".py") ||
      file.name.endsWith(".html") ||
      file.name.endsWith(".css")
    ) {
      reader.readAsText(file);
    } else {
      showNotification(
        "File type not supported for text extraction",
        "warning"
      );
    }
  }
}

function toggleVoiceInput() {
  if ("webkitSpeechRecognition" in window || "SpeechRecognition" in window) {
    startVoiceRecognition();
  } else {
    showNotification(
      "Speech recognition not supported in your browser",
      "warning"
    );
  }
}

function startVoiceRecognition() {
  const SpeechRecognition =
    window.SpeechRecognition || window.webkitSpeechRecognition;
  const recognition = new SpeechRecognition();

  recognition.continuous = false;
  recognition.interimResults = false;
  recognition.lang = "en-US";

  const voiceBtn = document.querySelector(".voice-btn");
  const icon = voiceBtn.querySelector("i");

  recognition.onstart = () => {
    icon.classList.replace("fa-microphone", "fa-stop");
    voiceBtn.style.color = "var(--primary-color)";
    showNotification("Listening... Speak now!", "info");
  };

  recognition.onresult = (event) => {
    const transcript = event.results[0][0].transcript;
    const messageInput = document.getElementById("messageInput");
    messageInput.value += transcript;
    autoResize(messageInput);
    showNotification("Voice input captured!", "success");
  };

  recognition.onerror = (event) => {
    showNotification("Voice recognition error: " + event.error, "error");
  };

  recognition.onend = () => {
    icon.classList.replace("fa-stop", "fa-microphone");
    voiceBtn.style.color = "";
  };

  recognition.start();
}

function copyMessage(button) {
  const messageText = button
    .closest(".message-content")
    .querySelector(".message-text").textContent;
  navigator.clipboard.writeText(messageText).then(() => {
    showNotification("Message copied to clipboard", "success");
  });
}

function regenerateResponse() {
  const messages = document.querySelectorAll(".message");
  const lastUserMessage = Array.from(messages)
    .reverse()
    .find((msg) => msg.classList.contains("user-message"));

  if (lastUserMessage) {
    const userText = lastUserMessage.querySelector(".message-text").textContent;

    // Remove the last AI response
    const lastAiMessage = document.querySelector(
      ".message.ai-message:last-child"
    );
    if (lastAiMessage) {
      lastAiMessage.remove();
    }

    // Remove from chat history
    const chat = chatHistory.find((c) => c.id === currentChatId);
    if (chat && chat.messages.length > 0) {
      chat.messages.pop(); // Remove last AI response
    }

    showNotification("Regenerating response...", "info");
    sendToAI(userText);
  } else {
    showNotification("No message to regenerate", "warning");
  }
}

function likeMessage(button) {
  button.classList.toggle("liked");
  const icon = button.querySelector("i");
  if (button.classList.contains("liked")) {
    icon.style.color = "var(--primary-color)";
    showNotification("Thanks for the feedback!", "success");
  } else {
    icon.style.color = "";
  }
}

// ===== Advanced Security Features =====
function showSecurityStatus() {
  const securityInfo = `🛡️ HAMMAD BHAI Security Status:

✅ Basic Security: Active
✅ Session Protection: Active
✅ Input Validation: Active
✅ Secure Storage: Active
✅ Data Protection: Active

🔒 Security Level: STANDARD
🕒 Session Valid: ${currentUser ? "Yes" : "No"}
👤 Current User: ${currentUser ? currentUser.username : "Not logged in"}
📊 Status: All systems operational`;

  showNotification(securityInfo, "success");
}

// ===== Advanced Features =====
function showForgotPassword() {
  // Remove existing modal if any
  const existingModal = document.querySelector(".forgot-password-modal");
  if (existingModal) {
    existingModal.remove();
  }

  const modal = document.createElement("div");
  modal.className = "forgot-password-modal";
  modal.innerHTML = `
    <div class="forgot-password-content">
      <div class="forgot-password-header">
        <h3><i class="fas fa-key"></i> Reset Password</h3>
        <button class="close-btn" onclick="closeForgotPasswordModal()">&times;</button>
      </div>
      <div class="forgot-password-body">
        <p>Enter your email address and we'll send you a link to reset your password.</p>
        <div class="form-group">
          <i class="fas fa-envelope"></i>
          <input
            type="email"
            id="resetEmail"
            placeholder="Enter your email"
            required
            autocomplete="email"
          />
        </div>
        <button class="auth-btn" onclick="handlePasswordReset()" id="resetBtn">
          <i class="fas fa-paper-plane"></i> Send Reset Link
        </button>
        <div class="forgot-password-info">
          <p><i class="fas fa-info-circle"></i> Password reset functionality is working! Enter any email to test.</p>
        </div>
        <div class="auth-links">
          <a href="#" onclick="closeForgotPasswordModal()">Back to Login</a>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // Focus on email input
  setTimeout(() => {
    const emailInput = document.getElementById("resetEmail");
    if (emailInput) {
      emailInput.focus();
    }
  }, 100);

  // Close modal when clicking outside
  modal.addEventListener("click", (e) => {
    if (e.target === modal) {
      closeForgotPasswordModal();
    }
  });

  // Close modal with Escape key
  document.addEventListener("keydown", function escapeHandler(e) {
    if (e.key === "Escape") {
      closeForgotPasswordModal();
      document.removeEventListener("keydown", escapeHandler);
    }
  });

  // Enter key to submit
  const emailInput = modal.querySelector("#resetEmail");
  emailInput.addEventListener("keypress", (e) => {
    if (e.key === "Enter") {
      handlePasswordReset();
    }
  });
}

function closeForgotPasswordModal() {
  const modal = document.querySelector(".forgot-password-modal");
  if (modal) {
    modal.style.opacity = "0";
    setTimeout(() => {
      modal.remove();
    }, 300);
  }
}

async function handlePasswordReset() {
  const email = document.getElementById("resetEmail").value.trim();
  const resetBtn = document.getElementById("resetBtn");

  if (!email) {
    showNotification("Please enter your email address", "error");
    document.getElementById("resetEmail").focus();
    return;
  }

  // Enhanced email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    showNotification("Please enter a valid email address", "error");
    document.getElementById("resetEmail").focus();
    return;
  }

  // Show loading state
  resetBtn.classList.add("loading");
  resetBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
  resetBtn.disabled = true;

  try {
    // Call real API
    const response = await fetch("/api/password-reset", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ email: email }),
    });

    const data = await response.json();

    if (response.ok && data.success) {
      // Show success message
      showNotification(`🔐 ${data.message}`, "success");

      // Show demo info if available
      if (data.demo_info) {
        setTimeout(() => {
          showNotification(`ℹ️ ${data.demo_info}`, "info");
        }, 2000);
      }

      // Close modal after success
      setTimeout(() => {
        closeForgotPasswordModal();
      }, 3000);
    } else {
      throw new Error(data.error || "Failed to send reset email");
    }
  } catch (error) {
    console.error("Password reset error:", error);
    showNotification(`❌ ${error.message}`, "error");
  } finally {
    // Reset button state
    resetBtn.classList.remove("loading");
    resetBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Send Reset Link';
    resetBtn.disabled = false;
  }
}

// Keyboard shortcuts
document.addEventListener("keydown", (e) => {
  // Ctrl/Cmd + K to focus search
  if ((e.ctrlKey || e.metaKey) && e.key === "k") {
    e.preventDefault();
    document.getElementById("chatSearch").focus();
  }

  // Ctrl/Cmd + N for new chat
  if ((e.ctrlKey || e.metaKey) && e.key === "n") {
    e.preventDefault();
    startNewChat();
  }

  // Escape to close modals
  if (e.key === "Escape") {
    const modal = document.querySelector(".export-modal");
    if (modal) {
      modal.remove();
    }
  }
});

// Auto-save functionality
setInterval(() => {
  if (chatHistory.length > 0) {
    saveChatHistory();
  }
}, 30000); // Auto-save every 30 seconds

// ===== Utility Functions =====
function generateSessionId() {
  return (
    "session_" + Math.random().toString(36).substr(2, 9) + "_" + Date.now()
  );
}

function generateChatId() {
  return "chat_" + Math.random().toString(36).substr(2, 9) + "_" + Date.now();
}

function loadSavedData() {
  // Load theme
  const savedTheme = localStorage.getItem("theme") || "dark";
  setTheme(savedTheme);

  // Load sidebar state
  const sidebarState = localStorage.getItem("sidebarCollapsed");
  if (sidebarState === "true") {
    sidebarCollapsed = true;
    document.getElementById("sidebar")?.classList.add("collapsed");
    document.getElementById("mainApp")?.classList.add("sidebar-collapsed");
  }
}

function saveChatHistory() {
  try {
    localStorage.setItem("chatHistory", JSON.stringify(chatHistory));
  } catch (error) {
    console.error("Failed to save chat history:", error);
  }
}

function loadChatHistory() {
  try {
    const saved = localStorage.getItem("chatHistory");
    if (saved) {
      chatHistory = JSON.parse(saved);
      updateChatHistoryUI();
    }
  } catch (error) {
    console.error("Failed to load chat history:", error);
    chatHistory = [];
  }
}

function clearChatMessages() {
  const chatMessages = document.getElementById("chatMessages");
  if (chatMessages) {
    chatMessages.innerHTML = "";
    chatMessages.classList.add("hidden");
  }
  showWelcomeScreen();
}

function showWelcomeScreen() {
  const welcomeScreen = document.getElementById("welcomeScreen");
  if (welcomeScreen) {
    welcomeScreen.classList.remove("hidden");
  }
}

function hideWelcomeScreen() {
  const welcomeScreen = document.getElementById("welcomeScreen");
  if (welcomeScreen) {
    welcomeScreen.classList.add("hidden");
  }
}

function updateChatTitle(title, subtitle) {
  const titleElement = document.getElementById("currentChatTitle");
  const subtitleElement = document.getElementById("chatSubtitle");

  if (titleElement) titleElement.textContent = title;
  if (subtitleElement) subtitleElement.textContent = subtitle;
}

function showNotification(message, type = "info") {
  // Create notification element
  const notification = document.createElement("div");
  notification.className = `notification notification-${type}`;
  notification.innerHTML = `
    <div class="notification-content">
      <span>${message}</span>
      <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
    </div>
  `;

  // Add to page
  document.body.appendChild(notification);

  // Auto remove after 5 seconds
  setTimeout(() => {
    if (notification.parentElement) {
      notification.remove();
    }
  }, 5000);
}

// ===== Missing Core Functions =====
function autoResize(textarea) {
  if (!textarea) return;

  textarea.style.height = "auto";
  textarea.style.height = Math.min(textarea.scrollHeight, 120) + "px";
}

function handleKeyDown(event) {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
}

function formatMessage(content) {
  // Basic markdown-like formatting
  return content
    .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
    .replace(/\*(.*?)\*/g, "<em>$1</em>")
    .replace(/`(.*?)`/g, "<code>$1</code>")
    .replace(/\n/g, "<br>");
}

function copyMessage(button) {
  const messageText = button
    .closest(".message-content")
    .querySelector(".message-text").textContent;
  navigator.clipboard.writeText(messageText).then(() => {
    showNotification("Message copied to clipboard!", "success");
  });
}

function editMessage(button) {
  const messageDiv = button.closest(".message");
  const messageText = messageDiv.querySelector(".message-text");
  const originalText = messageText.textContent;

  messageText.innerHTML = `<textarea class="edit-textarea">${originalText}</textarea>`;
  const textarea = messageText.querySelector(".edit-textarea");

  textarea.focus();
  textarea.addEventListener("blur", () => {
    messageText.textContent = textarea.value;
  });

  textarea.addEventListener("keydown", (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      messageText.textContent = textarea.value;
    }
  });
}

function deleteMessage(button) {
  if (confirm("Are you sure you want to delete this message?")) {
    button.closest(".message").remove();
    showNotification("Message deleted", "info");
  }
}

function regenerateResponse() {
  const messages = document.querySelectorAll(".message");
  const lastUserMessage = Array.from(messages)
    .reverse()
    .find((msg) => msg.classList.contains("user-message"));

  if (lastUserMessage) {
    const messageText =
      lastUserMessage.querySelector(".message-text").textContent;
    sendToAI(messageText);
  }
}

function likeMessage(button) {
  button.classList.toggle("liked");
  const icon = button.querySelector("i");
  if (button.classList.contains("liked")) {
    icon.className = "fas fa-thumbs-up";
    button.style.color = "var(--primary-color)";
    showNotification("Thanks for the feedback!", "success");
  } else {
    icon.className = "far fa-thumbs-up";
    button.style.color = "";
  }
}

function speakMessage(button) {
  const messageText = button
    .closest(".message-content")
    .querySelector(".message-text").textContent;

  if ("speechSynthesis" in window) {
    const utterance = new SpeechSynthesisUtterance(messageText);
    utterance.rate = 0.8;
    utterance.pitch = 1;
    speechSynthesis.speak(utterance);
    showNotification("Speaking message...", "info");
  } else {
    showNotification("Speech synthesis not supported", "error");
  }
}

// ===== File Upload Functions =====
function handleFileUpload() {
  const input = document.createElement("input");
  input.type = "file";
  input.accept = "image/*,.pdf,.txt,.doc,.docx";
  input.multiple = false;

  input.onchange = function (event) {
    const file = event.target.files[0];
    if (file) {
      if (file.size > 10 * 1024 * 1024) {
        // 10MB limit
        showNotification("File too large. Maximum size is 10MB.", "error");
        return;
      }

      showNotification(
        `File "${file.name}" selected. File upload feature coming soon!`,
        "info"
      );
    }
  };

  input.click();
}

// ===== Voice Input Functions =====
let isRecording = false;
let recognition = null;

function toggleVoiceInput() {
  if (
    !("webkitSpeechRecognition" in window) &&
    !("SpeechRecognition" in window)
  ) {
    showNotification("Voice input not supported in this browser", "error");
    return;
  }

  if (isRecording) {
    stopVoiceInput();
  } else {
    startVoiceInput();
  }
}

function startVoiceInput() {
  const SpeechRecognition =
    window.SpeechRecognition || window.webkitSpeechRecognition;
  recognition = new SpeechRecognition();

  recognition.continuous = false;
  recognition.interimResults = false;
  recognition.lang = "en-US";

  recognition.onstart = function () {
    isRecording = true;
    const voiceBtn = document.getElementById("voiceBtn");
    if (voiceBtn) {
      voiceBtn.classList.add("recording");
      voiceBtn.innerHTML = '<i class="fas fa-stop"></i>';
    }
    showNotification("Listening... Speak now!", "info");
  };

  recognition.onresult = function (event) {
    const transcript = event.results[0][0].transcript;
    const messageInput = document.getElementById("messageInput");
    if (messageInput) {
      messageInput.value = transcript;
      autoResize(messageInput);
      updateSendButton();
    }
    showNotification("Voice input captured!", "success");
  };

  recognition.onerror = function (event) {
    showNotification("Voice input error: " + event.error, "error");
    stopVoiceInput();
  };

  recognition.onend = function () {
    stopVoiceInput();
  };

  recognition.start();
}

function stopVoiceInput() {
  isRecording = false;
  if (recognition) {
    recognition.stop();
  }

  const voiceBtn = document.getElementById("voiceBtn");
  if (voiceBtn) {
    voiceBtn.classList.remove("recording");
    voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
  }
}

// ===== Event Listeners Setup =====
function setupEventListeners() {
  // Auth tabs with touch support
  document.querySelectorAll(".auth-tab").forEach((tab) => {
    tab.addEventListener("click", function () {
      const tabName = this.getAttribute("data-tab");
      switchAuthTab(tabName);
    });

    // Touch feedback
    tab.addEventListener("touchstart", function () {
      this.style.transform = "scale(0.95)";
    });

    tab.addEventListener("touchend", function () {
      this.style.transform = "";
    });
  });

  // Message input
  const messageInput = document.getElementById("messageInput");
  if (messageInput) {
    messageInput.addEventListener("input", function () {
      const sendBtn = document.getElementById("sendBtn");
      const charCount = document.getElementById("charCount");

      if (charCount) {
        charCount.textContent = this.value.length;
      }

      updateSendButton();
      autoResize(this);
    });

    messageInput.addEventListener("keydown", handleKeyDown);
  }

  // Send button
  const sendBtn = document.getElementById("sendBtn");
  if (sendBtn) {
    sendBtn.addEventListener("click", sendMessage);
  }

  // Enter key for login/register forms
  document
    .getElementById("loginPassword")
    ?.addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        handleLogin();
      }
    });

  document
    .getElementById("confirmPassword")
    ?.addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        handleRegister();
      }
    });
}

// Keyboard shortcuts
document.addEventListener("keydown", (e) => {
  // Ctrl/Cmd + K to focus search
  if ((e.ctrlKey || e.metaKey) && e.key === "k") {
    e.preventDefault();
    document.getElementById("chatSearch").focus();
  }

  // Ctrl/Cmd + N for new chat
  if ((e.ctrlKey || e.metaKey) && e.key === "n") {
    e.preventDefault();
    startNewChat();
  }

  // Ctrl/Cmd + E to export chats
  if ((e.ctrlKey || e.metaKey) && e.key === "e") {
    e.preventDefault();
    exportChats();
  }

  // Ctrl/Cmd + T to toggle theme
  if ((e.ctrlKey || e.metaKey) && e.key === "t") {
    e.preventDefault();
    toggleTheme();
  }

  // Escape to close modals
  if (e.key === "Escape") {
    const modal = document.querySelector(".export-modal");
    if (modal) {
      modal.remove();
    }
  }
});

// Auto-save functionality
setInterval(() => {
  if (chatHistory.length > 0) {
    saveChatHistory();
  }
}, 30000); // Auto-save every 30 seconds

// Better error handling for API calls
async function handleApiError(response) {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    const errorMessage =
      errorData.error || `HTTP ${response.status}: ${response.statusText}`;
    throw new Error(errorMessage);
  }
  return response;
}

// Connection status indicator
let isOnline = navigator.onLine;

window.addEventListener("online", () => {
  isOnline = true;
  showNotification("Connection restored", "success");
});

window.addEventListener("offline", () => {
  isOnline = false;
  showNotification("Connection lost. Working offline.", "warning");
});

// Performance optimization - debounce search
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Debounced search function
const debouncedSearch = debounce(searchChats, 300);

// Update search to use debounced version
function searchChats() {
  const query = document.getElementById("chatSearch").value.toLowerCase();
  const chatItems = document.querySelectorAll(".chat-item");

  chatItems.forEach((item) => {
    const title = item
      .querySelector(".chat-item-title")
      .textContent.toLowerCase();
    const preview = item
      .querySelector(".chat-item-preview")
      .textContent.toLowerCase();

    if (title.includes(query) || preview.includes(query)) {
      item.style.display = "flex";
    } else {
      item.style.display = "none";
    }
  });
}
