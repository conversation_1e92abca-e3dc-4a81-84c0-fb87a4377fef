/* ===== HAMMAD BHAI - Ultimate AI Assistant Styles ===== */
/* Created by: <PERSON><PERSON><PERSON>MMAD HAMMAD ZUBAIR */

/* ===== CSS Variables ===== */
:root {
  /* Vibrant Modern Colors */
  --primary-color: #00ffff;
  --primary-dark: #00e6e6;
  --secondary-color: #ff6b6b;
  --accent-color: #ffd93d;
  --success-color: #6bcf7f;
  --warning-color: #ffb347;
  --error-color: #ff6b6b;
  --info-color: #74c0fc;
  --purple: #a855f7;
  --pink: #ec4899;
  --orange: #f97316;
  --green: #22c55e;

  /* Modern Dark Theme */
  --bg-primary: #0a0a0f;
  --bg-secondary: #1a1a2e;
  --bg-tertiary: #16213e;
  --bg-hover: #0e3460;
  --text-primary: #ffffff;
  --text-secondary: #e2e8f0;
  --text-muted: #94a3b8;
  --border-color: #334155;
  --shadow-light: rgba(0, 245, 255, 0.15);
  --shadow-medium: rgba(0, 245, 255, 0.25);
  --shadow-heavy: rgba(0, 0, 0, 0.6);

  /* Glass Effect */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  /* Dynamic Gradients */
  --gradient-primary: linear-gradient(
    135deg,
    #00ffff 0%,
    #ff6b6b 50%,
    #a855f7 100%
  );
  --gradient-secondary: linear-gradient(135deg, #ffd93d 0%, #ff6b6b 100%);
  --gradient-accent: linear-gradient(135deg, #ec4899 0%, #f97316 100%);
  --gradient-success: linear-gradient(135deg, #6bcf7f 0%, #22c55e 100%);
  --gradient-rainbow: linear-gradient(
    135deg,
    #00ffff 0%,
    #ff6b6b 25%,
    #ffd93d 50%,
    #6bcf7f 75%,
    #a855f7 100%
  );
  --gradient-neon: linear-gradient(
    135deg,
    #00ffff 0%,
    #ff00ff 50%,
    #ffff00 100%
  );

  /* Enhanced Light Theme */
  --light-bg-primary: #ffffff;
  --light-bg-secondary: #f8fafc;
  --light-bg-tertiary: #f1f5f9;
  --light-bg-hover: #e2e8f0;
  --light-text-primary: #0f172a;
  --light-text-secondary: #334155;
  --light-text-muted: #64748b;
  --light-border-color: #e2e8f0;
  --light-shadow-light: rgba(15, 23, 42, 0.08);
  --light-shadow-medium: rgba(15, 23, 42, 0.12);
  --light-shadow-heavy: rgba(15, 23, 42, 0.25);
  --light-glass-bg: rgba(255, 255, 255, 0.8);
  --light-glass-border: rgba(15, 23, 42, 0.1);

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;

  /* Fonts */
  --font-primary: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  --font-mono: "JetBrains Mono", "Fira Code", "Consolas", monospace;

  /* Sidebar */
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 60px;
}

/* ===== Enhanced Light Theme Override ===== */
[data-theme="light"] {
  --bg-primary: var(--light-bg-primary);
  --bg-secondary: var(--light-bg-secondary);
  --bg-tertiary: var(--light-bg-tertiary);
  --bg-hover: var(--light-bg-hover);
  --text-primary: var(--light-text-primary);
  --text-secondary: var(--light-text-secondary);
  --text-muted: var(--light-text-muted);
  --border-color: var(--light-border-color);
  --shadow-light: var(--light-shadow-light);
  --shadow-medium: var(--light-shadow-medium);
  --shadow-heavy: var(--light-shadow-heavy);
  --glass-bg: var(--light-glass-bg);
  --glass-border: var(--light-glass-border);
}

[data-theme="light"] body {
  background-image: radial-gradient(
      circle at 20% 80%,
      rgba(59, 130, 246, 0.08) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(168, 85, 247, 0.08) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(245, 158, 11, 0.06) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 60% 70%,
      rgba(34, 197, 94, 0.06) 0%,
      transparent 50%
    );
}

/* ===== Base Styles ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  background: var(--bg-primary);
  background-image: radial-gradient(
      circle at 20% 80%,
      rgba(0, 255, 255, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(255, 107, 107, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(255, 217, 61, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 60% 70%,
      rgba(168, 85, 247, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 90% 10%,
      rgba(34, 197, 94, 0.08) 0%,
      transparent 50%
    );
  background-attachment: fixed;
  background-size: 100% 100%, 80% 80%, 120% 120%, 90% 90%, 110% 110%;
  animation: backgroundShift 20s ease-in-out infinite;
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
  transition: all var(--transition-normal);
}

@keyframes backgroundShift {
  0%,
  100% {
    background-position: 0% 0%, 100% 100%, 50% 50%, 80% 20%, 20% 80%;
  }
  25% {
    background-position: 20% 10%, 80% 90%, 60% 40%, 70% 30%, 30% 70%;
  }
  50% {
    background-position: 40% 20%, 60% 80%, 70% 30%, 60% 40%, 40% 60%;
  }
  75% {
    background-position: 60% 30%, 40% 70%, 80% 20%, 50% 50%, 50% 50%;
  }
}

/* ===== Scrollbar Styles ===== */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* ===== Utility Classes ===== */
.hidden {
  display: none !important;
}

.visible {
  display: block !important;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.text-gradient {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  color: white;
  box-shadow: 0 2px 8px var(--shadow-light);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px var(--shadow-medium);
}

.btn-secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-hover);
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--bg-hover);
  color: var(--text-primary);
}

/* ===== Loading Screen ===== */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  transition: opacity var(--transition-slow);
}

.loading-content {
  text-align: center;
  animation: fadeInUp 0.8s ease;
}

.loading-logo {
  font-size: 4rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
  animation: pulse 2s infinite;
}

.loading-text h2 {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: var(--spacing-sm);
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.loading-text p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
}

.loading-bar {
  width: 200px;
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin: 0 auto;
}

.loading-progress {
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--secondary-color)
  );
  border-radius: var(--radius-full);
  animation: loadingProgress 2s ease-in-out infinite;
}

@keyframes loadingProgress {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== Authentication Modal ===== */
.auth-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: var(--spacing-md);
}

.auth-content {
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xxl);
  width: 100%;
  max-width: 450px;
  box-shadow: 0 20px 60px var(--shadow-heavy);
  border: 1px solid var(--border-color);
  animation: modalSlideIn 0.5s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.auth-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.auth-header h2 {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: var(--spacing-sm);
  color: var(--primary-color);
}

.auth-header h2 i {
  margin-right: var(--spacing-sm);
}

.auth-header p {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.auth-tabs {
  display: flex;
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xs);
  margin-bottom: var(--spacing-xl);
}

.auth-tab {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  background: transparent;
  color: var(--text-secondary);
  font-family: inherit;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.auth-tab.active {
  background: var(--primary-color);
  color: white;
  box-shadow: 0 2px 8px var(--shadow-light);
}

.auth-forms {
  position: relative;
  min-height: 300px;
}

.auth-form {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  opacity: 0;
  transform: translateX(20px);
  transition: all var(--transition-normal);
  pointer-events: none;
}

.auth-form.active {
  opacity: 1;
  transform: translateX(0);
  pointer-events: all;
}

.form-group {
  position: relative;
  margin-bottom: var(--spacing-lg);
}

.form-group i {
  position: absolute;
  left: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  z-index: 1;
}

.form-group input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-md) var(--spacing-md) 3rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: inherit;
  font-size: 1rem;
  transition: all var(--transition-fast);
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--shadow-light);
}

.password-toggle {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.password-toggle:hover {
  color: var(--text-primary);
  background: var(--bg-hover);
}

.auth-btn {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-family: inherit;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-bottom: var(--spacing-lg);
}

.auth-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-medium);
}

.auth-btn.guest-btn {
  background: linear-gradient(
    135deg,
    var(--secondary-color),
    var(--accent-color)
  );
}

.auth-links {
  text-align: center;
}

.auth-links a {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.auth-links a:hover {
  text-decoration: underline;
}

.guest-info {
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-lg);
}

.guest-info i {
  font-size: 2rem;
  color: var(--info-color);
  margin-bottom: var(--spacing-md);
}

.guest-info h3 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.guest-info p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  font-size: 0.875rem;
}

.guest-info ul {
  list-style: none;
  text-align: left;
  max-width: 250px;
  margin: 0 auto;
}

.guest-info li {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  font-size: 0.875rem;
}

.guest-info li i.fa-check {
  color: var(--success-color);
}

.guest-info li i.fa-times {
  color: var(--error-color);
}

.auth-footer {
  text-align: center;
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--border-color);
}

.auth-footer p {
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: var(--spacing-md);
}

.auth-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.auth-stats span {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.75rem;
  color: var(--text-muted);
}

.auth-stats i {
  color: var(--primary-color);
}

/* ===== Main Application ===== */
.main-app {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

/* ===== Sidebar ===== */
.sidebar {
  width: var(--sidebar-width);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  border-right: 1px solid var(--glass-border);
  display: flex;
  flex-direction: column;
  transition: all var(--transition-normal);
  z-index: var(--z-sticky);
  box-shadow: var(--glass-shadow);
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 1.25rem;
  font-weight: 800;
  color: var(--primary-color);
}

.logo i {
  font-size: 1.5rem;
}

.sidebar.collapsed .logo-text {
  display: none;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.25rem;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.new-chat-section {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.new-chat-btn {
  width: 100%;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--gradient-rainbow);
  background-size: 200% 200%;
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-family: inherit;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.4),
    0 0 30px rgba(255, 107, 107, 0.2);
  position: relative;
  overflow: hidden;
  animation: gradientShift 3s ease-in-out infinite;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
}

@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.new-chat-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.new-chat-btn:hover::before {
  left: 100%;
}

.new-chat-btn:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 30px rgba(0, 255, 255, 0.6),
    0 0 40px rgba(255, 107, 107, 0.4), 0 0 60px rgba(255, 217, 61, 0.2);
  animation: gradientShift 1s ease-in-out infinite,
    pulse 2s ease-in-out infinite;
}

.sidebar.collapsed .new-chat-btn span {
  display: none;
}

.sidebar.collapsed .new-chat-btn {
  justify-content: center;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -100%;
    z-index: var(--z-modal);
  }

  .sidebar.open {
    left: 0;
  }

  .main-content {
    margin-left: 0 !important;
    width: 100%;
  }

  .auth-content {
    margin: var(--spacing-md);
    padding: var(--spacing-xl);
  }

  .auth-stats {
    gap: var(--spacing-md);
  }

  .auth-stats span {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .auth-tabs {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .auth-tab {
    text-align: center;
  }

  .guest-info ul {
    max-width: 200px;
  }
}

/* ===== Search Section ===== */
.search-section {
  padding: 0 var(--spacing-lg) var(--spacing-lg);
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: var(--spacing-md);
  color: var(--text-muted);
  z-index: 1;
}

.search-box input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) 2.5rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  font-family: inherit;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--shadow-light);
}

.sidebar.collapsed .search-section {
  display: none;
}

/* ===== Chat History ===== */
.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: 0 var(--spacing-lg);
}

.history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.history-header h3 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.history-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.action-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 0.875rem;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.action-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.sidebar.collapsed .history-header h3,
.sidebar.collapsed .history-actions {
  display: none;
}

.history-group {
  margin-bottom: var(--spacing-xl);
}

.group-header {
  margin-bottom: var(--spacing-md);
}

.group-header span {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.sidebar.collapsed .group-header {
  display: none;
}

.chat-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.chat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
}

.chat-item:hover {
  background: var(--bg-hover);
}

.chat-item.active {
  background: var(--primary-color);
  color: white;
}

.chat-item-icon {
  font-size: 1rem;
  color: var(--text-muted);
  min-width: 20px;
}

.chat-item.active .chat-item-icon {
  color: white;
}

.chat-item-content {
  flex: 1;
  min-width: 0;
}

.chat-item-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.chat-item.active .chat-item-title {
  color: white;
}

.chat-item-preview {
  font-size: 0.75rem;
  color: var(--text-muted);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-item.active .chat-item-preview {
  color: rgba(255, 255, 255, 0.8);
}

.chat-item-actions {
  display: flex;
  gap: var(--spacing-xs);
  opacity: 0;
  transition: all var(--transition-fast);
}

.chat-item:hover .chat-item-actions {
  opacity: 1;
}

.chat-action-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 0.75rem;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.chat-action-btn:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.chat-item.active .chat-action-btn {
  color: rgba(255, 255, 255, 0.8);
}

.chat-item.active .chat-action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.sidebar.collapsed .chat-item-content,
.sidebar.collapsed .chat-item-actions {
  display: none;
}

.sidebar.collapsed .chat-item {
  justify-content: center;
  padding: var(--spacing-sm);
}

/* ===== Sidebar Footer ===== */
.sidebar-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  margin-top: auto;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-bottom: var(--spacing-md);
}

.user-profile:hover {
  background: var(--bg-hover);
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.username {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-status {
  display: block;
  font-size: 0.75rem;
  color: var(--success-color);
}

.user-profile i.fa-chevron-up {
  color: var(--text-muted);
  font-size: 0.75rem;
  transition: all var(--transition-fast);
}

.user-profile.open i.fa-chevron-up {
  transform: rotate(180deg);
}

.sidebar.collapsed .user-info,
.sidebar.collapsed .user-profile i.fa-chevron-up {
  display: none;
}

.sidebar.collapsed .user-profile {
  justify-content: center;
}

.user-menu {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  box-shadow: 0 4px 16px var(--shadow-medium);
}

.user-menu a {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--text-primary);
  text-decoration: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.user-menu a:hover {
  background: var(--bg-hover);
  color: var(--primary-color);
}

.user-menu a i {
  width: 16px;
  text-align: center;
}

.app-info {
  text-align: center;
}

.info-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}

.info-stats span {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.7rem;
  color: var(--text-muted);
}

.info-stats i {
  color: var(--primary-color);
}

.creator-info {
  font-size: 0.7rem;
  color: var(--text-muted);
}

.creator-info strong {
  color: var(--primary-color);
}

.sidebar.collapsed .app-info {
  display: none;
}

/* ===== Main Content ===== */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  overflow: hidden;
  margin-left: var(--sidebar-width);
  transition: all var(--transition-normal);
  position: relative;
}

.main-app.sidebar-collapsed .main-content {
  margin-left: var(--sidebar-collapsed-width);
}

/* ===== Top Header ===== */
.top-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--glass-border);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  z-index: var(--z-sticky);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 1.25rem;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.mobile-menu-btn:hover {
  background: var(--bg-hover);
}

.chat-title h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.chat-title span {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.model-selector select {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  padding: var(--spacing-sm) var(--spacing-md);
  font-family: inherit;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.model-selector select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--shadow-light);
}

.theme-toggle,
.settings-btn {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  color: var(--text-primary);
  font-size: 1.125rem;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.theme-toggle::before,
.settings-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.theme-toggle:hover,
.settings-btn:hover {
  background: var(--gradient-primary);
  border-color: transparent;
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.4);
}

.theme-toggle:hover::before,
.settings-btn:hover::before {
  left: 100%;
}

@media (max-width: 768px) {
  .mobile-menu-btn {
    display: flex;
  }

  .header-right {
    gap: var(--spacing-sm);
  }

  .model-selector {
    display: none;
  }

  .chat-title h2 {
    font-size: 1.25rem;
  }
}

/* ===== Chat Container ===== */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  padding: 0 var(--spacing-lg);
}

/* ===== Welcome Screen ===== */
.welcome-screen {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
}

.welcome-content {
  max-width: 1000px;
  width: 100%;
  margin: 0 auto;
}

.welcome-logo {
  font-size: 4rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-lg);
  animation: float 3s ease-in-out infinite, glow 2s ease-in-out infinite;
  text-shadow: 0 0 20px rgba(0, 245, 255, 0.5);
}

.welcome-screen h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-screen p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xxl);
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xxl);
}

.quick-action {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  font-weight: 500;
}

.quick-action:hover {
  background: var(--bg-hover);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px var(--shadow-light);
}

.quick-action i {
  font-size: 1.25rem;
  color: var(--primary-color);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.feature-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  transition: all var(--transition-fast);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px var(--shadow-medium);
  border-color: var(--primary-color);
}

.feature-card i {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.feature-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.feature-card p {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

/* ===== Chat Messages ===== */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-xl) 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  max-width: 900px;
  margin: 0 auto;
  width: 100%;
}

.message {
  display: flex;
  gap: var(--spacing-md);
  max-width: 75%;
  width: fit-content;
  animation: messageSlideIn 0.5s ease, messageGlow 0.5s ease;
  position: relative;
  transition: all var(--transition-fast);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.message:hover {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.1);
  transform: translateY(-2px);
}

@keyframes messageGlow {
  0% {
    box-shadow: 0 0 0 rgba(0, 255, 255, 0);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
  }
  100% {
    box-shadow: 0 0 0 rgba(0, 255, 255, 0);
  }
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
  margin-left: auto;
}

.ai-message {
  align-self: flex-start;
  margin-right: auto;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.125rem;
  flex-shrink: 0;
}

.user-message .message-avatar {
  background: linear-gradient(
    135deg,
    var(--secondary-color),
    var(--accent-color)
  );
  color: white;
}

.ai-message .message-avatar {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  color: white;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
}

.message-sender {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.message-time {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.message-text {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  color: var(--text-primary);
  line-height: 1.6;
  word-wrap: break-word;
  min-width: 200px;
  max-width: 100%;
}

.user-message .message-text {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  color: white;
  border-color: transparent;
}

.message-text code {
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-family: var(--font-mono);
  font-size: 0.875rem;
}

.user-message .message-text code {
  background: rgba(255, 255, 255, 0.2);
}

.message-actions {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
  opacity: 0;
  transition: all var(--transition-fast);
}

.message:hover .message-actions {
  opacity: 1;
}

.message-action-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 0.875rem;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.message-action-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.message-action-btn.liked {
  color: var(--primary-color);
}

/* ===== Advanced Typing Indicator ===== */
.typing-indicator .message-content {
  display: flex;
  align-items: center;
}

.typing-dots {
  display: flex;
  gap: 6px;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.1);
}

.typing-dots span {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--gradient-primary);
  animation: advancedTyping 1.6s infinite ease-in-out;
  box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}
.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}
.typing-dots span:nth-child(3) {
  animation-delay: 0s;
}

@keyframes advancedTyping {
  0%,
  80%,
  100% {
    transform: scale(0.8) translateY(0);
    opacity: 0.6;
    box-shadow: 0 0 5px rgba(0, 255, 255, 0.3);
  }
  40% {
    transform: scale(1.2) translateY(-8px);
    opacity: 1;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.8);
  }
}

/* ===== Input Section ===== */
.input-section {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--glass-border);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  width: 100%;
}

.input-container {
  max-width: 900px;
  width: 100%;
  margin: 0 auto;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-sm);
  background: var(--glass-bg);
  backdrop-filter: blur(15px);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-sm);
  transition: all var(--transition-fast);
  box-shadow: var(--glass-shadow);
}

.input-wrapper:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--shadow-light);
}

.attachment-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 1.125rem;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.attachment-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

#messageInput {
  flex: 1;
  border: none;
  background: transparent;
  color: var(--text-primary);
  font-family: inherit;
  font-size: 1rem;
  line-height: 1.5;
  resize: none;
  outline: none;
  min-height: 24px;
  max-height: 120px;
  padding: var(--spacing-sm) 0;
}

#messageInput::placeholder {
  color: var(--text-muted);
}

.input-actions {
  display: flex;
  gap: var(--spacing-xs);
  align-items: center;
}

.voice-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 1.125rem;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.voice-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.send-btn {
  background: var(--bg-tertiary);
  border: none;
  color: var(--text-muted);
  font-size: 1.125rem;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.send-btn.active {
  background: var(--gradient-primary);
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4px 20px rgba(0, 245, 255, 0.4);
}

.send-btn.active:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 16px var(--shadow-medium);
}

.input-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: var(--spacing-sm);
  padding: 0 var(--spacing-sm);
}

.input-info {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.input-stats {
  font-size: 0.75rem;
  color: var(--text-muted);
}

/* ===== Notifications ===== */
.notification {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  box-shadow: 0 8px 32px var(--shadow-medium);
  z-index: var(--z-tooltip);
  max-width: 400px;
  animation-duration: 0.5s;
}

.notification-success {
  border-left: 4px solid var(--success-color);
}

.notification-error {
  border-left: 4px solid var(--error-color);
}

.notification-warning {
  border-left: 4px solid var(--warning-color);
}

.notification-info {
  border-left: 4px solid var(--info-color);
}

.notification i {
  font-size: 1.125rem;
}

.notification-success i {
  color: var(--success-color);
}
.notification-error i {
  color: var(--error-color);
}
.notification-warning i {
  color: var(--warning-color);
}
.notification-info i {
  color: var(--info-color);
}

.notification span {
  flex: 1;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.notification button {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.notification button:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

/* ===== Export Modal ===== */
.export-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  animation: fadeIn 0.3s ease;
}

.export-modal-content {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease;
}

.export-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.export-modal-header h3 {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.export-modal-header h3 i {
  color: var(--primary-color);
  margin-right: var(--spacing-sm);
}

.close-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 1.5rem;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.close-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.export-modal-body {
  padding: var(--spacing-lg);
}

.export-modal-body p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

.export-options {
  display: grid;
  gap: var(--spacing-md);
}

.export-option-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: left;
}

.export-option-btn:hover {
  background: var(--bg-hover);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px var(--shadow-light);
}

.export-option-btn i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.export-option-btn span {
  font-weight: 600;
  font-size: 1rem;
}

.export-option-btn small {
  color: var(--text-muted);
  font-size: 0.875rem;
  display: block;
  margin-top: 2px;
}

/* ===== Chat History Items ===== */
.chat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  margin-bottom: var(--spacing-xs);
}

.chat-item:hover {
  background: var(--bg-hover);
}

.chat-item.active {
  background: var(--primary-color);
  color: white;
}

.chat-item-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  color: var(--primary-color);
  flex-shrink: 0;
}

.chat-item.active .chat-item-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.chat-item-content {
  flex: 1;
  min-width: 0;
}

.chat-item-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-item.active .chat-item-title {
  color: white;
}

.chat-item-preview {
  font-size: 0.75rem;
  color: var(--text-muted);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

.chat-item.active .chat-item-preview {
  color: rgba(255, 255, 255, 0.8);
}

.chat-item-actions {
  display: flex;
  gap: var(--spacing-xs);
  opacity: 0;
  transition: all var(--transition-fast);
}

.chat-item:hover .chat-item-actions {
  opacity: 1;
}

.chat-action-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 0.75rem;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
}

.chat-action-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.chat-item.active .chat-action-btn {
  color: rgba(255, 255, 255, 0.8);
}

.chat-item.active .chat-action-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* ===== Animations ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(0, 245, 255, 0.6);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* ===== Mobile Responsive ===== */
@media (max-width: 768px) {
  .welcome-screen h1 {
    font-size: 2rem;
  }

  .welcome-logo {
    font-size: 3rem;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .message {
    max-width: 90%;
  }

  .chat-messages {
    padding: var(--spacing-md);
  }

  .input-section {
    padding: var(--spacing-md);
  }

  .input-container {
    max-width: 100%;
    padding: 0 var(--spacing-sm);
  }

  .notification {
    top: var(--spacing-md);
    right: var(--spacing-md);
    left: var(--spacing-md);
    max-width: none;
  }

  .export-modal-content {
    width: 95%;
    margin: var(--spacing-md);
  }
}

/* ===== Settings Modal ===== */
.settings-modal,
.profile-modal,
.help-modal,
.attachment-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  animation: fadeIn 0.3s ease;
}

.settings-modal-content,
.profile-modal-content,
.help-modal-content,
.attachment-modal-content {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease;
}

.settings-modal-header,
.profile-modal-header,
.help-modal-header,
.attachment-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.settings-modal-header h3,
.profile-modal-header h3,
.help-modal-header h3,
.attachment-modal-header h3 {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.settings-modal-header h3 i,
.profile-modal-header h3 i,
.help-modal-header h3 i,
.attachment-modal-header h3 i {
  color: var(--primary-color);
  margin-right: var(--spacing-sm);
}

.settings-modal-body,
.profile-modal-body,
.help-modal-body,
.attachment-modal-body {
  padding: var(--spacing-lg);
}

/* Settings Modal Specific */
.setting-group {
  margin-bottom: var(--spacing-xl);
}

.setting-group h4 {
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.setting-group h4 i {
  color: var(--primary-color);
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
}

.setting-item label {
  color: var(--text-primary);
  font-weight: 500;
}

.setting-item select {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: 0.875rem;
}

.shortcuts-list {
  display: grid;
  gap: var(--spacing-sm);
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
}

.shortcut-item span {
  color: var(--text-primary);
  font-size: 0.875rem;
}

.shortcut-item kbd {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  padding: 2px 6px;
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-family: var(--font-mono);
}

/* Profile Modal Specific */
.profile-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: var(--radius-full);
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
}

.profile-details h4 {
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 var(--spacing-xs) 0;
}

.profile-details p {
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-xs) 0;
}

.user-type {
  background: var(--primary-color);
  color: white;
  padding: 2px 8px;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  text-transform: uppercase;
  font-weight: 600;
}

.profile-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
}

.stat-item i {
  font-size: 1.5rem;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.stat-item span {
  display: block;
  color: var(--text-secondary);
  font-size: 0.875rem;
  margin-bottom: var(--spacing-xs);
}

.stat-item strong {
  display: block;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

/* Help Modal Specific */
.help-section {
  margin-bottom: var(--spacing-xl);
}

.help-section h4 {
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.help-section h4 i {
  color: var(--primary-color);
}

.help-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.help-section li {
  padding: var(--spacing-sm) 0;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-color);
}

.help-section li:last-child {
  border-bottom: none;
}

.help-section p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Attachment Modal Specific */
.attachment-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
}

.attachment-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xl);
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: center;
}

.attachment-option:hover {
  background: var(--bg-hover);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px var(--shadow-light);
}

.attachment-option i {
  font-size: 2rem;
  color: var(--primary-color);
}

.attachment-option span {
  font-weight: 600;
  color: var(--text-primary);
}

.attachment-option small {
  color: var(--text-muted);
  font-size: 0.75rem;
}

/* Mobile Responsive for Modals */
@media (max-width: 768px) {
  .settings-modal-content,
  .profile-modal-content,
  .help-modal-content,
  .attachment-modal-content {
    width: 95%;
    margin: var(--spacing-md);
    max-height: 90vh;
  }

  .profile-info {
    flex-direction: column;
    text-align: center;
  }

  .profile-stats {
    grid-template-columns: 1fr;
  }

  .attachment-options {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .welcome-screen {
    padding: var(--spacing-lg);
  }

  .welcome-screen h1 {
    font-size: 1.75rem;
  }

  .feature-card {
    padding: var(--spacing-lg);
  }

  .message-text {
    padding: var(--spacing-sm) var(--spacing-md);
    min-width: 150px;
  }

  .input-wrapper {
    padding: var(--spacing-xs);
  }
}

/* ===== Advanced Security Features ===== */
.security-indicator {
  position: fixed;
  bottom: var(--spacing-lg);
  left: var(--spacing-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.75rem;
  color: var(--success-color);
  z-index: var(--z-fixed);
  animation: securityPulse 3s ease-in-out infinite;
}

.security-indicator i {
  font-size: 0.875rem;
}

@keyframes securityPulse {
  0%,
  100% {
    box-shadow: 0 0 10px rgba(107, 207, 127, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(107, 207, 127, 0.6);
  }
}

/* ===== Advanced Message Features ===== */
.message-enhanced {
  position: relative;
}

.message-enhanced::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--gradient-primary);
  border-radius: var(--radius-lg);
  opacity: 0;
  transition: opacity var(--transition-fast);
  z-index: -1;
}

.message-enhanced:hover::before {
  opacity: 0.1;
}

/* ===== Advanced Chat Features ===== */
.chat-features {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
}

.feature-btn {
  background: var(--glass-bg);
  backdrop-filter: blur(5px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-md);
  color: var(--text-muted);
  font-size: 0.75rem;
  cursor: pointer;
  padding: var(--spacing-xs) var(--spacing-sm);
  transition: all var(--transition-fast);
}

.feature-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(0, 255, 255, 0.3);
}

.feature-btn.active {
  background: var(--gradient-primary);
  color: white;
  border-color: transparent;
}

/* ===== Security Alert System ===== */
.security-alert {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(15px);
  border: 2px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  z-index: var(--z-tooltip);
  max-width: 400px;
  animation: securitySlideIn 0.5s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.security-alert-success {
  border-left: 4px solid var(--success-color);
  box-shadow: 0 8px 32px rgba(107, 207, 127, 0.3);
}

.security-alert-warning {
  border-left: 4px solid var(--warning-color);
  box-shadow: 0 8px 32px rgba(255, 179, 71, 0.3);
}

.security-alert-error {
  border-left: 4px solid var(--error-color);
  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.3);
  animation: securityPulseError 1s ease-in-out infinite;
}

.security-alert-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 500;
}

.security-alert-content i {
  font-size: 1.125rem;
  color: var(--primary-color);
}

.security-alert-success .security-alert-content i {
  color: var(--success-color);
}

.security-alert-warning .security-alert-content i {
  color: var(--warning-color);
}

.security-alert-error .security-alert-content i {
  color: var(--error-color);
}

.security-alert-content button {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 1.25rem;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all var(--transition-fast);
  margin-left: auto;
}

.security-alert-content button:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

@keyframes securitySlideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes securityPulseError {
  0%,
  100% {
    box-shadow: 0 8px 32px rgba(255, 107, 107, 0.3);
  }
  50% {
    box-shadow: 0 8px 32px rgba(255, 107, 107, 0.6);
  }
}

/* ===== Enhanced Security Indicator ===== */
.security-indicator {
  position: fixed;
  bottom: var(--spacing-lg);
  left: var(--spacing-lg);
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.75rem;
  color: var(--success-color);
  z-index: var(--z-fixed);
  animation: securityPulse 3s ease-in-out infinite;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.security-indicator:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 20px rgba(107, 207, 127, 0.4);
}

.security-indicator i {
  font-size: 0.875rem;
}

.security-indicator::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(
    45deg,
    var(--success-color),
    var(--primary-color)
  );
  border-radius: var(--radius-lg);
  opacity: 0;
  transition: opacity var(--transition-fast);
  z-index: -1;
}

.security-indicator:hover::before {
  opacity: 0.1;
}

/* ===== Mobile Security Responsive ===== */
@media (max-width: 768px) {
  .security-indicator {
    bottom: var(--spacing-md);
    left: var(--spacing-md);
    font-size: 0.7rem;
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .security-alert {
    top: var(--spacing-md);
    right: var(--spacing-md);
    left: var(--spacing-md);
    max-width: none;
  }

  .security-alert-content {
    font-size: 0.8rem;
  }
}

/* ===== MODERN UI REDESIGN - HAMMAD BHAI ===== */

/* Enhanced Logo Animations */
@keyframes logoFloat {
  0%,
  100% {
    transform: translateY(0px) scale(1) rotate(0deg);
    filter: hue-rotate(0deg);
  }
  25% {
    transform: translateY(-8px) scale(1.02) rotate(1deg);
    filter: hue-rotate(90deg);
  }
  50% {
    transform: translateY(-15px) scale(1.05) rotate(0deg);
    filter: hue-rotate(180deg);
  }
  75% {
    transform: translateY(-8px) scale(1.02) rotate(-1deg);
    filter: hue-rotate(270deg);
  }
}

@keyframes logoGlow {
  0%,
  100% {
    filter: drop-shadow(0 0 20px rgba(0, 255, 255, 0.5)) brightness(1);
    text-shadow: 0 0 30px rgba(0, 255, 255, 0.6);
  }
  50% {
    filter: drop-shadow(0 0 40px rgba(168, 85, 247, 0.8)) brightness(1.2);
    text-shadow: 0 0 50px rgba(168, 85, 247, 0.8);
  }
}

@keyframes logoAura {
  0% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
  }
  100% {
    opacity: 0.7;
    transform: translate(-50%, -50%) scale(1.2) rotate(360deg);
  }
}

/* Modern Welcome Screen */
.welcome-screen {
  background: radial-gradient(
      circle at 20% 80%,
      rgba(0, 255, 255, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(168, 85, 247, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(245, 158, 11, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 60% 70%,
      rgba(34, 197, 94, 0.1) 0%,
      transparent 50%
    ) !important;
  position: relative !important;
  overflow: hidden !important;
}

.welcome-screen::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
      45deg,
      transparent 30%,
      rgba(0, 255, 255, 0.05) 50%,
      transparent 70%
    ),
    linear-gradient(
      -45deg,
      transparent 30%,
      rgba(168, 85, 247, 0.03) 50%,
      transparent 70%
    );
  animation: shimmerMove 4s ease-in-out infinite;
  z-index: 0;
}

@keyframes shimmerMove {
  0% {
    transform: translateX(-100%) translateY(-100%);
  }
  50% {
    transform: translateX(0%) translateY(0%);
  }
  100% {
    transform: translateX(100%) translateY(100%);
  }
}

.welcome-content {
  position: relative !important;
  z-index: 1 !important;
}

/* Enhanced Logo */
.welcome-logo {
  font-size: 6rem !important;
  font-weight: 900 !important;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--purple) 25%,
    var(--pink) 50%,
    var(--orange) 75%,
    var(--primary-color) 100%
  ) !important;
  background-size: 300% 300% !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  animation: logoFloat 6s ease-in-out infinite, logoGlow 4s ease-in-out infinite,
    gradientShift 3s ease-in-out infinite !important;
  letter-spacing: -3px !important;
  position: relative !important;
  margin-bottom: var(--spacing-xl) !important;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.welcome-logo::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 150%;
  height: 150%;
  background: radial-gradient(
    circle,
    rgba(0, 255, 255, 0.3) 0%,
    rgba(168, 85, 247, 0.2) 30%,
    transparent 70%
  );
  border-radius: 50%;
  animation: logoAura 3s ease-in-out infinite alternate;
  z-index: -1;
}

/* Modern Welcome Title */
.welcome-screen h1 {
  font-size: 3.5rem !important;
  font-weight: 800 !important;
  background: linear-gradient(
    135deg,
    var(--text-primary),
    var(--primary-color)
  ) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  margin-bottom: var(--spacing-lg) !important;
  animation: titleSlideIn 1s ease-out !important;
}

@keyframes titleSlideIn {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.welcome-screen p {
  font-size: 1.25rem !important;
  color: var(--text-secondary) !important;
  margin-bottom: var(--spacing-xl) !important;
  animation: fadeInUp 1.2s ease-out 0.3s both !important;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Quick Actions */
.quick-actions {
  animation: fadeInUp 1.4s ease-out 0.6s both !important;
}

.quick-action-btn {
  background: var(--glass-bg) !important;
  backdrop-filter: blur(15px) !important;
  border: 2px solid rgba(0, 255, 255, 0.2) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--spacing-lg) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
}

.quick-action-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.quick-action-btn:hover {
  transform: translateY(-8px) scale(1.02) !important;
  border-color: var(--primary-color) !important;
  box-shadow: 0 20px 40px rgba(0, 255, 255, 0.3) !important;
}

.quick-action-btn:hover::before {
  left: 100%;
}

.quick-action-btn i {
  font-size: 2rem !important;
  background: var(--gradient-primary) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  margin-bottom: var(--spacing-sm) !important;
}

/* Enhanced Features Grid */
.features-grid {
  animation: fadeInUp 1.6s ease-out 0.9s both !important;
}

.feature-card {
  background: var(--glass-bg) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  border-radius: var(--radius-xl) !important;
  padding: var(--spacing-xl) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
}

.feature-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 255, 255, 0.05) 0%,
    transparent 50%
  );
  opacity: 0;
  transition: opacity 0.3s;
}

.feature-card:hover {
  transform: translateY(-10px) !important;
  border-color: var(--primary-color) !important;
  box-shadow: 0 25px 50px rgba(0, 255, 255, 0.25) !important;
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-card i {
  font-size: 3rem !important;
  background: var(--gradient-primary) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  margin-bottom: var(--spacing-md) !important;
  animation: iconFloat 3s ease-in-out infinite !important;
}

@keyframes iconFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.feature-card h3 {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  color: var(--text-primary) !important;
  margin-bottom: var(--spacing-sm) !important;
}

.feature-card p {
  color: var(--text-secondary) !important;
  line-height: 1.6 !important;
}

/* ===== MODERN CHAT INTERFACE ===== */

/* Enhanced Chat Messages */
.chat-messages {
  background: linear-gradient(
    180deg,
    rgba(0, 255, 255, 0.02) 0%,
    transparent 50%,
    rgba(168, 85, 247, 0.02) 100%
  ) !important;
  position: relative !important;
}

.chat-messages::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 10% 20%,
      rgba(0, 255, 255, 0.03) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 90% 80%,
      rgba(168, 85, 247, 0.03) 0%,
      transparent 50%
    );
  pointer-events: none;
  z-index: 0;
}

.message {
  backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  position: relative !important;
  z-index: 1 !important;
  animation: messageSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

@keyframes messageSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.message:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 15px 45px rgba(0, 255, 255, 0.2) !important;
  border-color: rgba(0, 255, 255, 0.3) !important;
}

.message-text {
  background: rgba(26, 26, 46, 0.8) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  position: relative !important;
  overflow: hidden !important;
}

.message-text::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 255, 255, 0.05),
    transparent
  );
  transition: left 0.6s;
}

.message:hover .message-text::before {
  left: 100%;
}

.user-message .message-text {
  background: linear-gradient(
    135deg,
    rgba(0, 255, 255, 0.2) 0%,
    rgba(168, 85, 247, 0.2) 100%
  ) !important;
  border-color: rgba(0, 255, 255, 0.4) !important;
}

.ai-message .message-text {
  background: rgba(26, 26, 46, 0.9) !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

/* Enhanced Message Avatar */
.message-avatar {
  width: 45px !important;
  height: 45px !important;
  border-radius: var(--radius-full) !important;
  border: 2px solid rgba(0, 255, 255, 0.3) !important;
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.3) !important;
  animation: avatarPulse 3s ease-in-out infinite !important;
}

@keyframes avatarPulse {
  0%,
  100% {
    box-shadow: 0 4px 20px rgba(0, 255, 255, 0.3);
    border-color: rgba(0, 255, 255, 0.3);
  }
  50% {
    box-shadow: 0 4px 25px rgba(0, 255, 255, 0.5);
    border-color: rgba(0, 255, 255, 0.5);
  }
}

.user-message .message-avatar {
  background: var(--gradient-primary) !important;
  border-color: rgba(0, 255, 255, 0.5) !important;
}

.ai-message .message-avatar {
  background: linear-gradient(135deg, var(--purple), var(--pink)) !important;
  border-color: rgba(168, 85, 247, 0.5) !important;
}

/* Enhanced Message Actions */
.message-actions {
  backdrop-filter: blur(10px) !important;
  background: rgba(26, 26, 46, 0.8) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--spacing-xs) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.message-action-btn {
  background: transparent !important;
  border: 1px solid rgba(0, 255, 255, 0.2) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--spacing-xs) var(--spacing-sm) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.message-action-btn:hover {
  background: rgba(0, 255, 255, 0.1) !important;
  border-color: var(--primary-color) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3) !important;
}

/* ===== ENHANCED SIDEBAR ===== */
.sidebar {
  background: rgba(10, 10, 15, 0.95) !important;
  backdrop-filter: blur(25px) !important;
  border-right: 1px solid rgba(0, 255, 255, 0.2) !important;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3) !important;
}

.sidebar-header {
  background: rgba(0, 255, 255, 0.05) !important;
  border-bottom: 1px solid rgba(0, 255, 255, 0.2) !important;
  backdrop-filter: blur(15px) !important;
}

.logo {
  animation: logoGlow 3s ease-in-out infinite !important;
}

.new-chat-btn {
  background: var(--gradient-primary) !important;
  border: none !important;
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.3) !important;
  position: relative !important;
  overflow: hidden !important;
}

.new-chat-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.new-chat-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 30px rgba(0, 255, 255, 0.5) !important;
}

.new-chat-btn:hover::before {
  left: 100%;
}

.chat-item {
  background: rgba(255, 255, 255, 0.02) !important;
  border: 1px solid rgba(255, 255, 255, 0.05) !important;
  border-radius: var(--radius-lg) !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.chat-item:hover {
  background: rgba(0, 255, 255, 0.1) !important;
  border-color: rgba(0, 255, 255, 0.3) !important;
  transform: translateX(5px) !important;
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.2) !important;
}

.chat-item.active {
  background: var(--gradient-primary) !important;
  border-color: transparent !important;
  box-shadow: 0 4px 25px rgba(0, 255, 255, 0.4) !important;
}

/* ===== MODERN INPUT SECTION ===== */
.input-section {
  background: rgba(10, 10, 15, 0.95) !important;
  backdrop-filter: blur(25px) !important;
  border-top: 1px solid rgba(0, 255, 255, 0.2) !important;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3) !important;
}

.input-wrapper {
  background: rgba(26, 26, 46, 0.8) !important;
  backdrop-filter: blur(20px) !important;
  border: 2px solid rgba(0, 255, 255, 0.3) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
  position: relative !important;
  overflow: hidden !important;
}

.input-wrapper::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 255, 255, 0.05) 0%,
    transparent 50%
  );
  opacity: 0;
  transition: opacity 0.3s;
}

.input-wrapper:focus-within {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 4px rgba(0, 255, 255, 0.2),
    0 8px 32px rgba(0, 255, 255, 0.3) !important;
}

.input-wrapper:focus-within::before {
  opacity: 1;
}

#messageInput {
  background: transparent !important;
  color: var(--text-primary) !important;
  font-size: 1.1rem !important;
  line-height: 1.6 !important;
}

#messageInput::placeholder {
  color: var(--text-muted) !important;
  font-style: italic !important;
}

.send-btn.active {
  background: var(--gradient-primary) !important;
  animation: pulse 2s infinite, rotate 4s linear infinite !important;
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.5) !important;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 255, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 255, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 255, 255, 0);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* ===== ADVANCED MOBILE RESPONSIVE ===== */
@media (max-width: 768px) {
  /* Enhanced Mobile Welcome */
  .welcome-logo {
    font-size: 4rem !important;
    letter-spacing: -2px !important;
  }

  .welcome-screen h1 {
    font-size: 2.5rem !important;
  }

  .welcome-screen p {
    font-size: 1.1rem !important;
  }

  /* Mobile Quick Actions */
  .quick-actions {
    grid-template-columns: 1fr 1fr !important;
    gap: var(--spacing-md) !important;
  }

  .quick-action-btn {
    padding: var(--spacing-md) !important;
  }

  .quick-action-btn i {
    font-size: 1.5rem !important;
  }

  /* Mobile Features Grid */
  .features-grid {
    grid-template-columns: 1fr !important;
    gap: var(--spacing-lg) !important;
  }

  .feature-card {
    padding: var(--spacing-lg) !important;
  }

  .feature-card i {
    font-size: 2.5rem !important;
  }

  /* Mobile Chat Interface */
  .message {
    max-width: 95% !important;
  }

  .message-avatar {
    width: 35px !important;
    height: 35px !important;
  }

  .message-text {
    padding: var(--spacing-sm) var(--spacing-md) !important;
    min-width: 120px !important;
  }

  /* Mobile Input */
  .input-wrapper {
    border-width: 1px !important;
  }

  #messageInput {
    font-size: 1rem !important;
  }

  /* Mobile Sidebar */
  .sidebar {
    transform: translateX(-100%) !important;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }

  .sidebar.open {
    transform: translateX(0) !important;
  }

  /* Mobile Security */
  .security-indicator {
    bottom: var(--spacing-md) !important;
    left: var(--spacing-md) !important;
    font-size: 0.7rem !important;
    padding: var(--spacing-xs) var(--spacing-sm) !important;
  }

  .security-alert {
    top: var(--spacing-md) !important;
    right: var(--spacing-md) !important;
    left: var(--spacing-md) !important;
    max-width: none !important;
  }
}

/* ===== TABLET RESPONSIVE ===== */
@media (min-width: 769px) and (max-width: 1024px) {
  .welcome-logo {
    font-size: 5rem !important;
  }

  .quick-actions {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .message {
    max-width: 85% !important;
  }
}

/* ===== LARGE SCREEN ENHANCEMENTS ===== */
@media (min-width: 1440px) {
  .welcome-logo {
    font-size: 7rem !important;
  }

  .welcome-screen h1 {
    font-size: 4rem !important;
  }

  .chat-container {
    max-width: 1600px !important;
  }

  .chat-messages {
    max-width: 1200px !important;
  }

  .input-container {
    max-width: 1200px !important;
  }
}
