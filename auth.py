# HAMMAD BHAI AI Assistant - Advanced Authentication System
# Complete Security & User Management

from flask import request, jsonify, current_app
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity, get_jwt
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from models import db, User, UserActivity, SecurityEvent
from datetime import datetime, timedelta
import re
import secrets
import hashlib
import json
from user_agents import parse
import requests

class AuthenticationManager:
    """Advanced Authentication Manager with Security Features"""
    
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize authentication manager"""
        self.app = app
        
        # Password requirements
        self.password_min_length = 8
        self.password_require_uppercase = True
        self.password_require_lowercase = True
        self.password_require_numbers = True
        self.password_require_special = True
        
        # Security settings
        self.max_failed_attempts = 5
        self.lockout_duration = 30  # minutes
        self.token_expiry = 24  # hours
    
    def validate_email(self, email):
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def validate_username(self, username):
        """Validate username format"""
        if len(username) < 3 or len(username) > 30:
            return False, "Username must be between 3 and 30 characters"
        
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            return False, "Username can only contain letters, numbers, and underscores"
        
        return True, "Valid username"
    
    def validate_password(self, password):
        """Validate password strength"""
        errors = []
        
        if len(password) < self.password_min_length:
            errors.append(f"Password must be at least {self.password_min_length} characters long")
        
        if self.password_require_uppercase and not re.search(r'[A-Z]', password):
            errors.append("Password must contain at least one uppercase letter")
        
        if self.password_require_lowercase and not re.search(r'[a-z]', password):
            errors.append("Password must contain at least one lowercase letter")
        
        if self.password_require_numbers and not re.search(r'\d', password):
            errors.append("Password must contain at least one number")
        
        if self.password_require_special and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("Password must contain at least one special character")
        
        # Check for common passwords
        common_passwords = ['password', '123456', 'qwerty', 'admin', 'letmein']
        if password.lower() in common_passwords:
            errors.append("Password is too common")
        
        if errors:
            return False, errors
        return True, "Strong password"
    
    def get_client_info(self):
        """Get client information for security tracking"""
        user_agent = request.headers.get('User-Agent', '')
        parsed_ua = parse(user_agent)
        
        return {
            'ip_address': get_remote_address(),
            'user_agent': user_agent,
            'browser': f"{parsed_ua.browser.family} {parsed_ua.browser.version_string}",
            'os': f"{parsed_ua.os.family} {parsed_ua.os.version_string}",
            'device': parsed_ua.device.family,
            'is_mobile': parsed_ua.is_mobile,
            'is_tablet': parsed_ua.is_tablet,
            'is_pc': parsed_ua.is_pc
        }
    
    def log_activity(self, user_id, activity_type, description, risk_level='low', metadata=None):
        """Log user activity for security tracking"""
        try:
            client_info = self.get_client_info()
            
            activity = UserActivity(
                user_id=user_id,
                activity_type=activity_type,
                description=description,
                ip_address=client_info['ip_address'],
                user_agent=client_info['user_agent'],
                risk_level=risk_level
            )
            
            if metadata:
                activity.set_metadata({**metadata, **client_info})
            else:
                activity.set_metadata(client_info)
            
            db.session.add(activity)
            db.session.commit()
            
        except Exception as e:
            print(f"Failed to log activity: {e}")
    
    def log_security_event(self, event_type, severity, description, user_id=None, metadata=None):
        """Log security events"""
        try:
            client_info = self.get_client_info()
            
            event = SecurityEvent(
                user_id=user_id,
                event_type=event_type,
                severity=severity,
                description=description,
                ip_address=client_info['ip_address'],
                user_agent=client_info['user_agent']
            )
            
            if metadata:
                event.set_metadata({**metadata, **client_info})
            else:
                event.set_metadata(client_info)
            
            db.session.add(event)
            db.session.commit()
            
        except Exception as e:
            print(f"Failed to log security event: {e}")
    
    def register_user(self, username, email, password, full_name=None):
        """Register new user with security checks"""
        try:
            # Validate input
            if not self.validate_email(email):
                return {'success': False, 'error': 'Invalid email format'}
            
            username_valid, username_msg = self.validate_username(username)
            if not username_valid:
                return {'success': False, 'error': username_msg}
            
            password_valid, password_msg = self.validate_password(password)
            if not password_valid:
                return {'success': False, 'error': password_msg}
            
            # Check if user already exists
            if User.query.filter_by(email=email).first():
                self.log_security_event(
                    'registration_attempt_duplicate_email',
                    'medium',
                    f'Registration attempt with existing email: {email}'
                )
                return {'success': False, 'error': 'Email already registered'}
            
            if User.query.filter_by(username=username).first():
                return {'success': False, 'error': 'Username already taken'}
            
            # Create new user
            user = User(
                username=username,
                email=email,
                full_name=full_name
            )
            user.set_password(password)
            
            # Generate verification token
            verification_token = user.generate_verification_token()
            
            db.session.add(user)
            db.session.commit()
            
            # Log successful registration
            self.log_activity(
                user.id,
                'user_registration',
                f'User {username} registered successfully',
                'low',
                {'email': email, 'verification_token': verification_token}
            )
            
            return {
                'success': True,
                'message': 'User registered successfully',
                'user_id': user.id,
                'verification_token': verification_token,
                'user': user.to_dict()
            }
            
        except Exception as e:
            db.session.rollback()
            self.log_security_event(
                'registration_error',
                'high',
                f'Registration failed: {str(e)}',
                metadata={'username': username, 'email': email}
            )
            return {'success': False, 'error': 'Registration failed'}
    
    def login_user(self, email_or_username, password):
        """Login user with security checks"""
        try:
            # Find user by email or username
            user = User.query.filter(
                (User.email == email_or_username) | 
                (User.username == email_or_username)
            ).first()
            
            if not user:
                self.log_security_event(
                    'login_attempt_invalid_user',
                    'medium',
                    f'Login attempt with invalid user: {email_or_username}'
                )
                return {'success': False, 'error': 'Invalid credentials'}
            
            # Check if account is locked
            if user.is_account_locked():
                self.log_security_event(
                    'login_attempt_locked_account',
                    'high',
                    f'Login attempt on locked account: {user.username}',
                    user.id
                )
                return {
                    'success': False, 
                    'error': f'Account locked until {user.account_locked_until.strftime("%Y-%m-%d %H:%M:%S")}'
                }
            
            # Check if account is banned
            if user.is_banned:
                self.log_security_event(
                    'login_attempt_banned_account',
                    'critical',
                    f'Login attempt on banned account: {user.username}',
                    user.id
                )
                return {'success': False, 'error': 'Account has been banned'}
            
            # Check password
            if not user.check_password(password):
                user.record_login_attempt(success=False, ip_address=get_remote_address())
                db.session.commit()
                
                self.log_security_event(
                    'login_attempt_invalid_password',
                    'medium',
                    f'Invalid password for user: {user.username}',
                    user.id
                )
                
                return {'success': False, 'error': 'Invalid credentials'}
            
            # Successful login
            user.record_login_attempt(success=True, ip_address=get_remote_address())
            db.session.commit()
            
            # Create JWT token
            access_token = create_access_token(
                identity=user.id,
                expires_delta=timedelta(hours=self.token_expiry)
            )
            
            # Log successful login
            self.log_activity(
                user.id,
                'user_login',
                f'User {user.username} logged in successfully',
                'low'
            )
            
            return {
                'success': True,
                'message': 'Login successful',
                'access_token': access_token,
                'user': user.to_dict()
            }
            
        except Exception as e:
            self.log_security_event(
                'login_error',
                'high',
                f'Login failed: {str(e)}',
                metadata={'email_or_username': email_or_username}
            )
            return {'success': False, 'error': 'Login failed'}
    
    def reset_password(self, email):
        """Initiate password reset"""
        try:
            user = User.query.filter_by(email=email).first()
            
            if not user:
                # Don't reveal if email exists
                return {
                    'success': True,
                    'message': 'If the email exists, a reset link has been sent'
                }
            
            # Generate reset token
            reset_token = user.generate_reset_token()
            db.session.commit()
            
            # Log password reset request
            self.log_activity(
                user.id,
                'password_reset_request',
                f'Password reset requested for {user.username}',
                'medium',
                {'reset_token': reset_token}
            )
            
            return {
                'success': True,
                'message': 'Password reset link sent',
                'reset_token': reset_token,
                'user_id': user.id
            }
            
        except Exception as e:
            self.log_security_event(
                'password_reset_error',
                'high',
                f'Password reset failed: {str(e)}',
                metadata={'email': email}
            )
            return {'success': False, 'error': 'Password reset failed'}

# Initialize authentication manager
auth_manager = AuthenticationManager()
