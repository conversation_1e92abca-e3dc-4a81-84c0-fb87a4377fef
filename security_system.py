#!/usr/bin/env python3
"""
HAMMAD BHAI AI Assistant - ENTERPRISE SECURITY SYSTEM
Created by: MUHAMMAD HAMMAD ZUBAIR
Advanced security protection against all types of attacks
"""

import os
import time
import hashlib
import secrets
import bcrypt
import jwt
import sqlite3
import ipaddress
from datetime import datetime, timedelta
from functools import wraps
from flask import request, jsonify, g
from cryptography.fernet import Fernet
import re

class AdvancedSecuritySystem:
    def __init__(self, app=None):
        self.app = app
        self.failed_attempts = {}  # IP -> {count, last_attempt, blocked_until}
        self.active_sessions = {}  # token -> session_info
        self.suspicious_ips = set()
        self.blocked_ips = set()
        self.encryption_key = self._generate_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
        
        # Security thresholds
        self.MAX_FAILED_ATTEMPTS = 5
        self.BLOCK_DURATION = 3600  # 1 hour
        self.SESSION_TIMEOUT = 86400  # 24 hours
        self.MAX_REQUEST_SIZE = 1024 * 1024  # 1MB
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """Initialize security system with Flask app"""
        self.app = app
        self._setup_security_database()
        self._setup_security_headers()
        
    def _generate_encryption_key(self):
        """Generate or load encryption key"""
        key_file = 'security.key'
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            return key
    
    def _setup_security_database(self):
        """Setup security database tables"""
        try:
            conn = sqlite3.connect('security.db')
            cursor = conn.cursor()
            
            # Security events table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS security_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_type TEXT NOT NULL,
                    ip_address TEXT NOT NULL,
                    user_agent TEXT,
                    details TEXT,
                    risk_level TEXT DEFAULT 'medium',
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    resolved INTEGER DEFAULT 0
                )
            ''')
            
            # Failed login attempts
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS failed_attempts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ip_address TEXT NOT NULL,
                    attempt_type TEXT NOT NULL,
                    details TEXT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Active sessions
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS active_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_token TEXT UNIQUE NOT NULL,
                    user_id TEXT,
                    ip_address TEXT NOT NULL,
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active INTEGER DEFAULT 1
                )
            ''')
            
            # Blocked IPs
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS blocked_ips (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ip_address TEXT UNIQUE NOT NULL,
                    reason TEXT NOT NULL,
                    blocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    blocked_until TIMESTAMP,
                    is_permanent INTEGER DEFAULT 0
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ Security database initialized successfully!")
            
        except Exception as e:
            print(f"❌ Security database error: {e}")
    
    def _setup_security_headers(self):
        """Setup security headers for all responses"""
        @self.app.after_request
        def add_security_headers(response):
            # Prevent XSS attacks
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['X-XSS-Protection'] = '1; mode=block'
            
            # HTTPS enforcement
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
            
            # Content Security Policy
            response.headers['Content-Security-Policy'] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; "
                "style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; "
                "font-src 'self' https://cdnjs.cloudflare.com; "
                "img-src 'self' data: https:; "
                "connect-src 'self'"
            )
            
            # Prevent information disclosure
            response.headers['Server'] = 'HAMMAD-BHAI-SECURE'
            response.headers['X-Powered-By'] = 'HAMMAD-BHAI-AI'
            
            return response
    
    def get_client_ip(self):
        """Get real client IP address"""
        # Check for forwarded IP (behind proxy/load balancer)
        if request.headers.get('X-Forwarded-For'):
            ip = request.headers.get('X-Forwarded-For').split(',')[0].strip()
        elif request.headers.get('X-Real-IP'):
            ip = request.headers.get('X-Real-IP')
        else:
            ip = request.remote_addr
        
        # Validate IP address
        try:
            ipaddress.ip_address(ip)
            return ip
        except:
            return '127.0.0.1'  # Fallback to localhost
    
    def is_ip_blocked(self, ip_address):
        """Check if IP is blocked"""
        try:
            conn = sqlite3.connect('security.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT blocked_until, is_permanent FROM blocked_ips 
                WHERE ip_address = ? AND (is_permanent = 1 OR blocked_until > datetime('now'))
            ''', (ip_address,))
            
            result = cursor.fetchone()
            conn.close()
            
            return result is not None
            
        except Exception as e:
            print(f"❌ IP block check error: {e}")
            return False
    
    def block_ip(self, ip_address, reason, duration_hours=1, permanent=False):
        """Block IP address"""
        try:
            conn = sqlite3.connect('security.db')
            cursor = conn.cursor()
            
            blocked_until = None if permanent else datetime.now() + timedelta(hours=duration_hours)
            
            cursor.execute('''
                INSERT OR REPLACE INTO blocked_ips 
                (ip_address, reason, blocked_until, is_permanent)
                VALUES (?, ?, ?, ?)
            ''', (ip_address, reason, blocked_until, 1 if permanent else 0))
            
            conn.commit()
            conn.close()
            
            self.log_security_event('ip_blocked', ip_address, f"IP blocked: {reason}", 'high')
            print(f"🚫 IP blocked: {ip_address} - {reason}")
            
        except Exception as e:
            print(f"❌ IP blocking error: {e}")
    
    def log_security_event(self, event_type, ip_address, details, risk_level='medium'):
        """Log security event"""
        try:
            conn = sqlite3.connect('security.db')
            cursor = conn.cursor()
            
            user_agent = request.headers.get('User-Agent', '') if request else ''
            
            cursor.execute('''
                INSERT INTO security_events 
                (event_type, ip_address, user_agent, details, risk_level)
                VALUES (?, ?, ?, ?, ?)
            ''', (event_type, ip_address, user_agent, details, risk_level))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ Security logging error: {e}")
    
    def record_failed_attempt(self, ip_address, attempt_type, details=""):
        """Record failed login/access attempt"""
        try:
            conn = sqlite3.connect('security.db')
            cursor = conn.cursor()
            
            # Record the attempt
            cursor.execute('''
                INSERT INTO failed_attempts (ip_address, attempt_type, details)
                VALUES (?, ?, ?)
            ''', (ip_address, attempt_type, details))
            
            # Count recent attempts (last hour)
            cursor.execute('''
                SELECT COUNT(*) FROM failed_attempts 
                WHERE ip_address = ? AND timestamp > datetime('now', '-1 hour')
            ''', (ip_address,))
            
            attempt_count = cursor.fetchone()[0]
            conn.commit()
            conn.close()
            
            # Block IP if too many attempts
            if attempt_count >= self.MAX_FAILED_ATTEMPTS:
                self.block_ip(ip_address, f"Too many failed attempts ({attempt_count})", 1)
                return True  # IP was blocked
            
            return False  # IP not blocked yet
            
        except Exception as e:
            print(f"❌ Failed attempt recording error: {e}")
            return False
    
    def validate_input(self, data, input_type='general'):
        """Validate and sanitize input data"""
        if not data:
            return False, "Empty input"
        
        # Check for common injection patterns
        dangerous_patterns = [
            r'<script[^>]*>.*?</script>',  # XSS
            r'javascript:',  # XSS
            r'on\w+\s*=',  # Event handlers
            r'union\s+select',  # SQL injection
            r'drop\s+table',  # SQL injection
            r'insert\s+into',  # SQL injection
            r'delete\s+from',  # SQL injection
            r'\.\./.*',  # Path traversal
            r'file://',  # File inclusion
            r'data:text/html',  # Data URI XSS
        ]
        
        data_str = str(data).lower()
        for pattern in dangerous_patterns:
            if re.search(pattern, data_str, re.IGNORECASE):
                self.log_security_event('malicious_input', self.get_client_ip(), 
                                      f"Dangerous pattern detected: {pattern}", 'high')
                return False, f"Potentially malicious input detected"
        
        # Input type specific validation
        if input_type == 'email':
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, data):
                return False, "Invalid email format"
        
        elif input_type == 'username':
            if not re.match(r'^[a-zA-Z0-9_]{3,20}$', data):
                return False, "Username must be 3-20 characters, alphanumeric and underscore only"
        
        elif input_type == 'password':
            if len(data) < 8:
                return False, "Password must be at least 8 characters"
            if not re.search(r'[A-Z]', data):
                return False, "Password must contain uppercase letter"
            if not re.search(r'[a-z]', data):
                return False, "Password must contain lowercase letter"
            if not re.search(r'\d', data):
                return False, "Password must contain number"
        
        return True, "Valid input"
    
    def encrypt_data(self, data):
        """Encrypt sensitive data"""
        try:
            if isinstance(data, str):
                data = data.encode()
            return self.cipher_suite.encrypt(data).decode()
        except Exception as e:
            print(f"❌ Encryption error: {e}")
            return None
    
    def decrypt_data(self, encrypted_data):
        """Decrypt sensitive data"""
        try:
            if isinstance(encrypted_data, str):
                encrypted_data = encrypted_data.encode()
            return self.cipher_suite.decrypt(encrypted_data).decode()
        except Exception as e:
            print(f"❌ Decryption error: {e}")
            return None
    
    def generate_secure_token(self, user_id, additional_data=None):
        """Generate secure JWT token"""
        try:
            payload = {
                'user_id': user_id,
                'iat': datetime.utcnow(),
                'exp': datetime.utcnow() + timedelta(hours=24),
                'ip': self.get_client_ip(),
                'jti': secrets.token_urlsafe(16)  # Unique token ID
            }
            
            if additional_data:
                payload.update(additional_data)
            
            secret_key = os.getenv('JWT_SECRET', 'hammad-bhai-ultra-secure-key-2024')
            token = jwt.encode(payload, secret_key, algorithm='HS256')
            
            # Store active session
            self._store_active_session(token, user_id)
            
            return token
            
        except Exception as e:
            print(f"❌ Token generation error: {e}")
            return None
    
    def verify_token(self, token):
        """Verify JWT token"""
        try:
            secret_key = os.getenv('JWT_SECRET', 'hammad-bhai-ultra-secure-key-2024')
            payload = jwt.decode(token, secret_key, algorithms=['HS256'])
            
            # Check if session is still active
            if not self._is_session_active(token):
                return None, "Session expired or invalid"
            
            # Verify IP address (optional - can be disabled for mobile users)
            current_ip = self.get_client_ip()
            token_ip = payload.get('ip')
            
            # Update last activity
            self._update_session_activity(token)
            
            return payload, None
            
        except jwt.ExpiredSignatureError:
            return None, "Token expired"
        except jwt.InvalidTokenError:
            return None, "Invalid token"
        except Exception as e:
            print(f"❌ Token verification error: {e}")
            return None, "Token verification failed"
    
    def _store_active_session(self, token, user_id):
        """Store active session in database"""
        try:
            conn = sqlite3.connect('security.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO active_sessions 
                (session_token, user_id, ip_address, user_agent)
                VALUES (?, ?, ?, ?)
            ''', (token, user_id, self.get_client_ip(), request.headers.get('User-Agent', '')))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ Session storage error: {e}")
    
    def _is_session_active(self, token):
        """Check if session is active"""
        try:
            conn = sqlite3.connect('security.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT is_active FROM active_sessions 
                WHERE session_token = ? AND is_active = 1
            ''', (token,))
            
            result = cursor.fetchone()
            conn.close()
            
            return result is not None
            
        except Exception as e:
            print(f"❌ Session check error: {e}")
            return False
    
    def _update_session_activity(self, token):
        """Update session last activity"""
        try:
            conn = sqlite3.connect('security.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE active_sessions 
                SET last_activity = CURRENT_TIMESTAMP 
                WHERE session_token = ?
            ''', (token,))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ Session update error: {e}")
    
    def invalidate_session(self, token):
        """Invalidate session (logout)"""
        try:
            conn = sqlite3.connect('security.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE active_sessions 
                SET is_active = 0 
                WHERE session_token = ?
            ''', (token,))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"❌ Session invalidation error: {e}")
            return False

# Security decorators
def require_auth(f):
    """Decorator to require authentication"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'error': 'Authentication required'}), 401
        
        if token.startswith('Bearer '):
            token = token[7:]
        
        payload, error = g.security.verify_token(token)
        if error:
            return jsonify({'error': error}), 401
        
        g.current_user = payload
        return f(*args, **kwargs)
    
    return decorated_function

def check_ip_blocked(f):
    """Decorator to check if IP is blocked"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        ip = g.security.get_client_ip()
        if g.security.is_ip_blocked(ip):
            g.security.log_security_event('blocked_access_attempt', ip, 
                                        'Blocked IP attempted access', 'high')
            return jsonify({'error': 'Access denied'}), 403
        
        return f(*args, **kwargs)
    
    return decorated_function

def validate_request_size(max_size=1024*1024):  # 1MB default
    """Decorator to validate request size"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if request.content_length and request.content_length > max_size:
                g.security.log_security_event('large_request', g.security.get_client_ip(),
                                            f'Request too large: {request.content_length}', 'medium')
                return jsonify({'error': 'Request too large'}), 413
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

print("✅ Advanced Security System loaded successfully!")
