# HAMMAD BHAI AI Assistant - Database Models
# Advanced User Management & Security System

from flask_sqlalchemy import SQLAlchemy
from flask_bcrypt import Bcrypt
from datetime import datetime, timedelta
import secrets
import json
from sqlalchemy import text

db = SQLAlchemy()
bcrypt = Bcrypt()

class User(db.Model):
    """Advanced User Model with Security Features"""
    __tablename__ = 'users'

    # Primary Information
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(128), nullable=False)

    # Profile Information
    full_name = db.Column(db.String(200))
    avatar_url = db.Column(db.String(500))
    bio = db.Column(db.Text)

    # Account Status
    is_active = db.Column(db.Bo<PERSON>, default=True)
    is_verified = db.Column(db.Boolean, default=False)
    is_admin = db.Column(db.Boolean, default=False)
    is_banned = db.Column(db.Boolean, default=False)

    # Security Information
    email_verification_token = db.Column(db.String(100))
    email_verification_sent_at = db.Column(db.DateTime)
    password_reset_token = db.Column(db.String(100))
    password_reset_sent_at = db.Column(db.DateTime)
    last_password_change = db.Column(db.DateTime, default=datetime.utcnow)

    # Login Information
    last_login = db.Column(db.DateTime)
    last_login_ip = db.Column(db.String(45))
    login_count = db.Column(db.Integer, default=0)
    failed_login_attempts = db.Column(db.Integer, default=0)
    account_locked_until = db.Column(db.DateTime)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    activity_logs = db.relationship('UserActivity', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    chat_sessions = db.relationship('ChatSession', backref='user', lazy='dynamic', cascade='all, delete-orphan')

    def set_password(self, password):
        """Hash and set password"""
        self.password_hash = bcrypt.generate_password_hash(password).decode('utf-8')
        self.last_password_change = datetime.utcnow()

    def check_password(self, password):
        """Check if password is correct"""
        return bcrypt.check_password_hash(self.password_hash, password)

    def generate_verification_token(self):
        """Generate email verification token"""
        self.email_verification_token = secrets.token_urlsafe(32)
        self.email_verification_sent_at = datetime.utcnow()
        return self.email_verification_token

    def generate_reset_token(self):
        """Generate password reset token"""
        self.password_reset_token = secrets.token_urlsafe(32)
        self.password_reset_sent_at = datetime.utcnow()
        return self.password_reset_token

    def is_account_locked(self):
        """Check if account is locked due to failed attempts"""
        if self.account_locked_until:
            return datetime.utcnow() < self.account_locked_until
        return False

    def lock_account(self, duration_minutes=30):
        """Lock account for specified duration"""
        self.account_locked_until = datetime.utcnow() + timedelta(minutes=duration_minutes)
        self.failed_login_attempts = 0

    def unlock_account(self):
        """Unlock account"""
        self.account_locked_until = None
        self.failed_login_attempts = 0

    def record_login_attempt(self, success=True, ip_address=None):
        """Record login attempt"""
        if success:
            self.last_login = datetime.utcnow()
            self.last_login_ip = ip_address
            self.login_count += 1
            self.failed_login_attempts = 0
            self.unlock_account()
        else:
            self.failed_login_attempts += 1
            if self.failed_login_attempts >= 5:
                self.lock_account()

    def to_dict(self):
        """Convert user to dictionary"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'full_name': self.full_name,
            'avatar_url': self.avatar_url,
            'bio': self.bio,
            'is_active': self.is_active,
            'is_verified': self.is_verified,
            'is_admin': self.is_admin,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'login_count': self.login_count,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

    def __repr__(self):
        return f'<User {self.username}>'

class UserActivity(db.Model):
    """User Activity Tracking for Security"""
    __tablename__ = 'user_activities'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # Activity Information
    activity_type = db.Column(db.String(50), nullable=False)  # login, logout, password_change, etc.
    description = db.Column(db.Text)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)

    # Location Information (if available)
    country = db.Column(db.String(100))
    city = db.Column(db.String(100))

    # Risk Assessment
    risk_level = db.Column(db.String(20), default='low')  # low, medium, high, critical
    is_suspicious = db.Column(db.Boolean, default=False)

    # Additional Data
    extra_data = db.Column(db.Text)  # JSON string for additional data

    # Timestamp
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def set_metadata(self, data):
        """Set metadata as JSON"""
        self.extra_data = json.dumps(data)

    def get_metadata(self):
        """Get metadata as dictionary"""
        if self.extra_data:
            return json.loads(self.extra_data)
        return {}

    def __repr__(self):
        return f'<UserActivity {self.activity_type} by User {self.user_id}>'

class ChatSession(db.Model):
    """Chat Session Tracking"""
    __tablename__ = 'chat_sessions'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # Nullable for guest users
    session_id = db.Column(db.String(100), unique=True, nullable=False)

    # Session Information
    title = db.Column(db.String(200))
    message_count = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)

    # Security Information
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_activity = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    messages = db.relationship('ChatMessage', backref='session', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<ChatSession {self.session_id}>'

class ChatMessage(db.Model):
    """Individual Chat Messages"""
    __tablename__ = 'chat_messages'

    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.Integer, db.ForeignKey('chat_sessions.id'), nullable=False)

    # Message Information
    message_type = db.Column(db.String(20), nullable=False)  # user, assistant, system
    content = db.Column(db.Text, nullable=False)

    # AI Information
    model_used = db.Column(db.String(100))
    tokens_used = db.Column(db.Integer)
    response_time = db.Column(db.Float)

    # Content Analysis
    is_flagged = db.Column(db.Boolean, default=False)
    flag_reason = db.Column(db.String(200))

    # Timestamp
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<ChatMessage {self.message_type} in Session {self.session_id}>'

class SecurityEvent(db.Model):
    """Security Events and Threats"""
    __tablename__ = 'security_events'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)

    # Event Information
    event_type = db.Column(db.String(50), nullable=False)  # failed_login, suspicious_activity, etc.
    severity = db.Column(db.String(20), nullable=False)  # low, medium, high, critical
    description = db.Column(db.Text, nullable=False)

    # Network Information
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)

    # Response Information
    action_taken = db.Column(db.String(100))
    is_resolved = db.Column(db.Boolean, default=False)

    # Additional Data
    extra_data = db.Column(db.Text)  # JSON string

    # Timestamp
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def set_metadata(self, data):
        """Set metadata as JSON"""
        self.extra_data = json.dumps(data)

    def get_metadata(self):
        """Get metadata as dictionary"""
        if self.extra_data:
            return json.loads(self.extra_data)
        return {}

    def __repr__(self):
        return f'<SecurityEvent {self.event_type} - {self.severity}>'
