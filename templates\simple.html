<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HAMMAD BHAI AI Assistant - Simple Mode</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Simple mode specific styles */
        .auth-modal {
            display: none !important;
        }
        
        .main-container {
            display: flex !important;
        }
        
        .simple-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: var(--spacing-md);
            text-align: center;
            margin-bottom: var(--spacing-lg);
            border-radius: var(--radius-lg);
        }
        
        .simple-header h1 {
            margin: 0;
            font-size: 1.5rem;
        }
        
        .simple-header p {
            margin: var(--spacing-sm) 0 0 0;
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .simple-controls {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
            justify-content: center;
        }
        
        .simple-btn {
            padding: var(--spacing-sm) var(--spacing-md);
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .simple-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }
        
        .typing-dots {
            display: flex;
            gap: 4px;
            padding: var(--spacing-sm);
        }
        
        .typing-dots span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--primary-color);
            animation: typing 1.4s infinite ease-in-out;
        }
        
        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }
        
        .status-bar {
            background: var(--bg-tertiary);
            padding: var(--spacing-sm);
            text-align: center;
            font-size: 0.8rem;
            color: var(--text-secondary);
            border-radius: var(--radius-sm);
            margin-bottom: var(--spacing-md);
        }
        
        .status-online {
            color: var(--success-color);
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Sidebar (simplified) -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-robot"></i> HAMMAD BHAI AI</h2>
                <p>Simple Mode - No Authentication</p>
            </div>
            
            <div class="sidebar-content">
                <div class="simple-controls">
                    <button class="simple-btn" onclick="clearChat()">
                        <i class="fas fa-trash"></i> Clear Chat
                    </button>
                </div>
                
                <div class="status-bar">
                    <i class="fas fa-circle status-online"></i>
                    Connected - Simple Mode
                </div>
                
                <div class="sidebar-section">
                    <h3><i class="fas fa-info-circle"></i> Features</h3>
                    <ul>
                        <li>✅ Direct Chat Access</li>
                        <li>✅ No Registration Required</li>
                        <li>✅ Full AI Capabilities</li>
                        <li>✅ Real-time Responses</li>
                        <li>✅ Chat History</li>
                    </ul>
                </div>
                
                <div class="sidebar-section">
                    <h3><i class="fas fa-user"></i> User Info</h3>
                    <p><strong>User:</strong> <span class="user-name">Guest User</span></p>
                    <p><strong>Mode:</strong> Simple (No Auth)</p>
                    <p><strong>Access:</strong> Full Features</p>
                </div>
            </div>
        </div>

        <!-- Main Chat Area -->
        <div class="chat-container">
            <div class="simple-header">
                <h1><i class="fas fa-robot"></i> HAMMAD BHAI AI Assistant</h1>
                <p>Simple Mode - Direct Access | Created by: Muhammad Hammad Zubair</p>
            </div>
            
            <div class="chat-header">
                <div class="chat-title">
                    <h2><i class="fas fa-comments"></i> Chat</h2>
                    <span class="chat-status">
                        <i class="fas fa-circle status-online"></i> Online
                    </span>
                </div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <!-- Messages will be added here dynamically -->
            </div>

            <div class="chat-input-container">
                <div class="chat-input-wrapper">
                    <textarea 
                        id="chatInput" 
                        class="chat-input" 
                        placeholder="Type your message here... (Press Enter to send)"
                        rows="1"
                    ></textarea>
                    <button id="sendButton" class="send-button">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Load simple JavaScript -->
    <script src="{{ url_for('static', filename='js/simple.js') }}"></script>
    
    <script>
        // Auto-resize textarea
        document.getElementById('chatInput').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });
        
        // Focus on input when page loads
        window.addEventListener('load', function() {
            document.getElementById('chatInput').focus();
        });
        
        console.log('✅ HAMMAD BHAI AI - Simple Mode Loaded');
    </script>
</body>
</html>
