<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Test - HAMMAD BHAI AI Assistant</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .test-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--bg-primary);
            padding: var(--spacing-lg);
        }
        
        .test-card {
            background: var(--bg-secondary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            max-width: 600px;
            width: 100%;
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-lg);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }
        
        .test-header h1 {
            color: var(--primary-color);
            margin-bottom: var(--spacing-sm);
            font-size: 2rem;
        }
        
        .test-header p {
            color: var(--text-secondary);
            margin-bottom: 0;
        }
        
        .status-box {
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            border-left: 4px solid var(--primary-color);
        }
        
        .status-item {
            display: flex;
            align-items: center;
            margin-bottom: var(--spacing-sm);
            color: var(--text-secondary);
        }
        
        .status-item:last-child {
            margin-bottom: 0;
        }
        
        .status-item i {
            margin-right: var(--spacing-sm);
            width: 20px;
        }
        
        .status-success {
            color: var(--success-color);
        }
        
        .status-error {
            color: var(--error-color);
        }
        
        .test-btn {
            width: 100%;
            padding: var(--spacing-md);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            border-radius: var(--radius-md);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: var(--spacing-lg);
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .test-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .result-box {
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
            display: none;
        }
        
        .result-success {
            border-left: 4px solid var(--success-color);
        }
        
        .result-error {
            border-left: 4px solid var(--error-color);
        }
        
        .setup-guide {
            background: var(--bg-tertiary);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        
        .setup-guide h3 {
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
        }
        
        .setup-guide ol {
            color: var(--text-secondary);
            padding-left: var(--spacing-lg);
        }
        
        .setup-guide li {
            margin-bottom: var(--spacing-sm);
        }
        
        .back-link {
            text-align: center;
        }
        
        .back-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
        }
        
        .back-link a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .test-container {
                padding: var(--spacing-md);
            }
            
            .test-card {
                padding: var(--spacing-lg);
            }
            
            .test-header h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-card">
            <div class="test-header">
                <h1><i class="fas fa-envelope-open"></i> Email System Test</h1>
                <p>Test your Gmail configuration for password reset emails</p>
            </div>
            
            <div class="status-box">
                <div class="status-item">
                    <i class="fas fa-database"></i>
                    <span>Database: Connected</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-shield-alt"></i>
                    <span>Authentication: Working</span>
                </div>
                <div class="status-item" id="emailStatus">
                    <i class="fas fa-envelope"></i>
                    <span>Email: Checking...</span>
                </div>
            </div>
            
            <button class="test-btn" onclick="testEmail()" id="testBtn">
                <i class="fas fa-paper-plane"></i> Test Email Sending
            </button>
            
            <div class="result-box" id="resultBox"></div>
            
            <div class="setup-guide">
                <h3><i class="fas fa-cog"></i> Gmail Setup Required</h3>
                <ol>
                    <li>Go to <strong>https://myaccount.google.com/</strong></li>
                    <li>Enable <strong>2-Factor Authentication</strong> first</li>
                    <li>Go to <strong>Security → App passwords</strong></li>
                    <li>Generate password for <strong>"Mail"</strong></li>
                    <li>Copy the 16-character password</li>
                    <li>Update <strong>.env</strong> file with your App Password</li>
                    <li>Restart the server</li>
                    <li>Test email sending</li>
                </ol>
            </div>
            
            <div class="back-link">
                <a href="/"><i class="fas fa-arrow-left"></i> Back to Main App</a>
            </div>
        </div>
    </div>

    <script>
        // Check email status on page load
        window.addEventListener('load', () => {
            checkEmailStatus();
        });
        
        function checkEmailStatus() {
            const statusElement = document.getElementById('emailStatus');
            
            // Check if email is configured
            fetch('/api/test-email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: '<EMAIL>' })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error && data.error.includes('not configured')) {
                    statusElement.innerHTML = '<i class="fas fa-exclamation-triangle status-error"></i><span>Email: Not Configured</span>';
                } else {
                    statusElement.innerHTML = '<i class="fas fa-check-circle status-success"></i><span>Email: Ready to Test</span>';
                }
            })
            .catch(error => {
                statusElement.innerHTML = '<i class="fas fa-times-circle status-error"></i><span>Email: Error</span>';
            });
        }
        
        async function testEmail() {
            const testBtn = document.getElementById('testBtn');
            const resultBox = document.getElementById('resultBox');
            
            // Show loading
            testBtn.disabled = true;
            testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending Test Email...';
            
            try {
                const response = await fetch('/api/test-email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>'
                    })
                });
                
                const data = await response.json();
                
                resultBox.style.display = 'block';
                
                if (response.ok && data.success) {
                    resultBox.className = 'result-box result-success';
                    resultBox.innerHTML = `
                        <h4 style="color: var(--success-color); margin-bottom: var(--spacing-sm);">
                            <i class="fas fa-check-circle"></i> Email Test Successful!
                        </h4>
                        <p style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">
                            ${data.message}
                        </p>
                        <p style="color: var(--text-secondary); font-size: 0.9rem;">
                            Check your Gmail inbox for the test email. Password reset emails will now work!
                        </p>
                    `;
                    
                    // Update status
                    document.getElementById('emailStatus').innerHTML = 
                        '<i class="fas fa-check-circle status-success"></i><span>Email: Working Perfectly</span>';
                        
                } else {
                    resultBox.className = 'result-box result-error';
                    resultBox.innerHTML = `
                        <h4 style="color: var(--error-color); margin-bottom: var(--spacing-sm);">
                            <i class="fas fa-times-circle"></i> Email Test Failed
                        </h4>
                        <p style="color: var(--text-secondary); margin-bottom: var(--spacing-sm);">
                            ${data.error}
                        </p>
                        <div style="background: var(--bg-primary); padding: var(--spacing-md); border-radius: var(--radius-sm); margin-top: var(--spacing-sm);">
                            <p style="color: var(--text-muted); font-size: 0.9rem; margin: 0;">
                                <strong>Troubleshooting:</strong><br>
                                • Make sure 2-Factor Authentication is enabled<br>
                                • Use App Password, not regular Gmail password<br>
                                • Check EMAIL_ADDRESS and EMAIL_PASSWORD in .env file
                            </p>
                        </div>
                    `;
                }
                
            } catch (error) {
                resultBox.style.display = 'block';
                resultBox.className = 'result-box result-error';
                resultBox.innerHTML = `
                    <h4 style="color: var(--error-color); margin-bottom: var(--spacing-sm);">
                        <i class="fas fa-times-circle"></i> Network Error
                    </h4>
                    <p style="color: var(--text-secondary);">
                        Failed to connect to server. Please try again.
                    </p>
                `;
            } finally {
                testBtn.disabled = false;
                testBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Test Email Sending';
            }
        }
    </script>
</body>
</html>
