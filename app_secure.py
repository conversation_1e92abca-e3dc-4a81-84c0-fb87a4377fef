#!/usr/bin/env python3
"""
HAMMAD BHAI AI Assistant - ENTERPRISE SECURITY VERSION
Created by: <PERSON><PERSON><PERSON><PERSON>AD HAMMAD ZUBAIR
Ultra-secure AI Assistant with military-grade protection
"""

import os
import time
import secrets
import smtplib
import bcrypt
import sqlite3
import hashlib
from datetime import datetime, timedelta
from flask import Flask, render_template, request, jsonify, g
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_cors import CORS
from flask_talisman import Talisman
import google.generativeai as genai

# Import our advanced security system
from security_system import AdvancedSecuritySystem, require_auth, check_ip_blocked, validate_request_size

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ .env file loaded successfully!")
except ImportError:
    print("⚠️ python-dotenv not installed. Using system environment variables.")

# Configuration
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
if not GEMINI_API_KEY:
    print("❌ GEMINI_API_KEY not found in environment variables!")
    exit(1)

# Configure Gemini
genai.configure(api_key=GEMINI_API_KEY)

# ------------ SECURE DATABASE FUNCTIONS ------------

def init_secure_database():
    """Initialize secure database with encryption"""
    try:
        conn = sqlite3.connect('hammad_bhai_secure.db')
        cursor = conn.cursor()
        
        # Users table with enhanced security
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                salt TEXT NOT NULL,
                full_name TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP,
                login_attempts INTEGER DEFAULT 0,
                account_locked_until TIMESTAMP,
                is_active INTEGER DEFAULT 1,
                is_verified INTEGER DEFAULT 0,
                verification_token TEXT,
                two_factor_secret TEXT,
                recovery_codes TEXT
            )
        ''')
        
        # Enhanced sessions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_token TEXT UNIQUE NOT NULL,
                ip_address TEXT NOT NULL,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                is_active INTEGER DEFAULT 1,
                device_fingerprint TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Security audit log
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS security_audit (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action TEXT NOT NULL,
                ip_address TEXT NOT NULL,
                user_agent TEXT,
                details TEXT,
                risk_level TEXT DEFAULT 'low',
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ Secure database initialized successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Secure database error: {e}")
        return False

def hash_password_secure(password):
    """Secure password hashing with bcrypt and salt"""
    try:
        # Generate salt
        salt = bcrypt.gensalt(rounds=12)
        # Hash password
        password_hash = bcrypt.hashpw(password.encode('utf-8'), salt)
        return password_hash.decode('utf-8'), salt.decode('utf-8')
    except Exception as e:
        print(f"❌ Password hashing error: {e}")
        return None, None

def verify_password_secure(password, stored_hash, stored_salt):
    """Verify password with secure hashing"""
    try:
        return bcrypt.checkpw(password.encode('utf-8'), stored_hash.encode('utf-8'))
    except Exception as e:
        print(f"❌ Password verification error: {e}")
        return False

def create_secure_user(username, email, password, full_name=""):
    """Create user with enhanced security"""
    try:
        # Validate input
        security = g.security
        
        # Validate username
        valid, msg = security.validate_input(username, 'username')
        if not valid:
            return {'success': False, 'error': f'Invalid username: {msg}'}
        
        # Validate email
        valid, msg = security.validate_input(email, 'email')
        if not valid:
            return {'success': False, 'error': f'Invalid email: {msg}'}
        
        # Validate password
        valid, msg = security.validate_input(password, 'password')
        if not valid:
            return {'success': False, 'error': f'Invalid password: {msg}'}
        
        conn = sqlite3.connect('hammad_bhai_secure.db')
        cursor = conn.cursor()
        
        # Check if user exists
        cursor.execute('SELECT id FROM users WHERE email = ? OR username = ?', (email, username))
        if cursor.fetchone():
            conn.close()
            security.record_failed_attempt(security.get_client_ip(), 'duplicate_registration', 
                                         f'Attempted to register existing user: {email}')
            return {'success': False, 'error': 'User already exists'}
        
        # Hash password securely
        password_hash, salt = hash_password_secure(password)
        if not password_hash:
            conn.close()
            return {'success': False, 'error': 'Password hashing failed'}
        
        # Generate verification token
        verification_token = secrets.token_urlsafe(32)
        
        # Create user
        cursor.execute('''
            INSERT INTO users (username, email, password_hash, salt, full_name, verification_token)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (username, email, password_hash, salt, full_name, verification_token))
        
        user_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        # Log security event
        security.log_security_event('user_registration', security.get_client_ip(),
                                  f'New user registered: {username}', 'low')
        
        return {
            'success': True,
            'user': {
                'id': user_id,
                'username': username,
                'email': email,
                'full_name': full_name,
                'verification_token': verification_token
            }
        }
        
    except Exception as e:
        print(f"❌ Secure user creation error: {e}")
        return {'success': False, 'error': 'User creation failed'}

def authenticate_secure_user(email_or_username, password):
    """Authenticate user with enhanced security"""
    try:
        security = g.security
        ip_address = security.get_client_ip()
        
        conn = sqlite3.connect('hammad_bhai_secure.db')
        cursor = conn.cursor()
        
        # Get user data
        cursor.execute('''
            SELECT id, username, email, password_hash, salt, login_attempts, 
                   account_locked_until, is_active, is_verified
            FROM users 
            WHERE email = ? OR username = ?
        ''', (email_or_username, email_or_username))
        
        user = cursor.fetchone()
        
        if not user:
            conn.close()
            security.record_failed_attempt(ip_address, 'invalid_user', email_or_username)
            return {'success': False, 'error': 'Invalid credentials'}
        
        user_id, username, email, stored_hash, salt, login_attempts, locked_until, is_active, is_verified = user
        
        # Check if account is locked
        if locked_until and datetime.fromisoformat(locked_until) > datetime.now():
            conn.close()
            security.log_security_event('locked_account_access', ip_address,
                                      f'Attempted access to locked account: {username}', 'high')
            return {'success': False, 'error': 'Account temporarily locked'}
        
        # Verify password
        if not verify_password_secure(password, stored_hash, salt):
            # Increment failed attempts
            new_attempts = login_attempts + 1
            locked_until_time = None
            
            if new_attempts >= 5:
                locked_until_time = datetime.now() + timedelta(hours=1)
                security.log_security_event('account_locked', ip_address,
                                          f'Account locked due to failed attempts: {username}', 'high')
            
            cursor.execute('''
                UPDATE users 
                SET login_attempts = ?, account_locked_until = ?
                WHERE id = ?
            ''', (new_attempts, locked_until_time, user_id))
            
            conn.commit()
            conn.close()
            
            security.record_failed_attempt(ip_address, 'invalid_password', username)
            return {'success': False, 'error': 'Invalid credentials'}
        
        # Reset failed attempts on successful login
        cursor.execute('''
            UPDATE users 
            SET login_attempts = 0, account_locked_until = NULL, last_login = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (user_id,))
        
        conn.commit()
        conn.close()
        
        # Generate secure session token
        token = security.generate_secure_token(user_id, {'username': username, 'email': email})
        
        # Log successful login
        security.log_security_event('successful_login', ip_address,
                                  f'User logged in: {username}', 'low')
        
        return {
            'success': True,
            'user': {
                'id': user_id,
                'username': username,
                'email': email,
                'is_verified': bool(is_verified)
            },
            'token': token
        }
        
    except Exception as e:
        print(f"❌ Secure authentication error: {e}")
        return {'success': False, 'error': 'Authentication failed'}

# ------------ SECURE GEMINI MODEL ------------

class SecureGeminiModel:
    def __init__(self, api_key, model_name='gemini-2.5-flash-preview-05-20'):
        self.api_key = api_key
        self.model_name = model_name
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name)
        print(f"✅ Secure Gemini model initialized: {model_name}")

    def generate_response(self, message, user_context=None):
        try:
            # Add security context to prompt
            secure_prompt = f"""
            [SECURITY CONTEXT: User authenticated, content filtered]
            User message: {message}
            
            Please provide a helpful and safe response. Do not include any harmful, illegal, or inappropriate content.
            """
            
            response = self.model.generate_content(secure_prompt)
            return response.text
        except Exception as e:
            return f"I apologize, but I encountered an error processing your request. Please try again."

# ------------ SECURE FLASK APP ------------

# Initialize Flask app
app = Flask(__name__)

# Enhanced security configuration
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', secrets.token_urlsafe(32))
app.config['SESSION_COOKIE_SECURE'] = True
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)

# Initialize security systems
security_system = AdvancedSecuritySystem(app)

# CORS with security
CORS(app, origins=['http://localhost:5000'], supports_credentials=True)

# Security headers with Talisman
Talisman(app, force_https=False)  # Set to True in production

# Rate limiting
limiter = Limiter(
    app=app,
    key_func=get_remote_address,
    default_limits=["100 per hour", "20 per minute"]
)

# Initialize secure database
init_secure_database()

# Initialize secure chatbot
try:
    gemini_model = SecureGeminiModel(api_key=GEMINI_API_KEY)
    print("✅ Secure system initialized successfully!")
except Exception as e:
    print(f"❌ Secure initialization error: {e}")
    gemini_model = None

# Security middleware
@app.before_request
def security_middleware():
    """Security checks before each request"""
    g.security = security_system
    
    # Check if IP is blocked
    if security_system.is_ip_blocked(security_system.get_client_ip()):
        return jsonify({'error': 'Access denied'}), 403
    
    # Validate request size
    if request.content_length and request.content_length > 1024 * 1024:  # 1MB
        return jsonify({'error': 'Request too large'}), 413

print("✅ HAMMAD BHAI AI - ENTERPRISE SECURITY VERSION loaded successfully!")
