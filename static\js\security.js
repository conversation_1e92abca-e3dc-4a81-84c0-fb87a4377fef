// ===== HAMMAD BHAI - Advanced Security System =====
// Created by: MUHAMMAD HAMMAD ZUBAIR
// Ultimate Protection Against All Attacks

class AdvancedSecurity {
  constructor() {
    this.encryptionKey = this.generateSecureKey();
    this.sessionToken = this.generateSessionToken();
    this.lastActivity = Date.now();
    this.maxInactivity = 15 * 60 * 1000; // 15 minutes
    this.failedAttempts = 0;
    this.maxFailedAttempts = 5;
    this.blockedIPs = new Set();
    this.suspiciousActivity = [];
    this.initAdvancedSecurity();
  }

  // ===== Encryption & Decryption =====
  generateSecureKey() {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  generateSessionToken() {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  advancedEncrypt(data) {
    try {
      const jsonString = JSON.stringify(data);
      const timestamp = Date.now().toString();
      const combined = timestamp + '|' + jsonString;
      
      // Multi-layer encryption
      let encrypted = btoa(unescape(encodeURIComponent(combined)));
      encrypted = this.xorEncrypt(encrypted, this.encryptionKey);
      encrypted = btoa(encrypted);
      
      return encrypted;
    } catch (error) {
      console.error('Encryption failed:', error);
      this.logSecurityEvent('ENCRYPTION_FAILED', error.message);
      return null;
    }
  }

  advancedDecrypt(encryptedData) {
    try {
      let decrypted = atob(encryptedData);
      decrypted = this.xorDecrypt(decrypted, this.encryptionKey);
      decrypted = decodeURIComponent(escape(atob(decrypted)));
      
      const [timestamp, jsonString] = decrypted.split('|');
      const dataAge = Date.now() - parseInt(timestamp);
      
      // Check if data is too old (24 hours)
      if (dataAge > 24 * 60 * 60 * 1000) {
        this.logSecurityEvent('EXPIRED_DATA', 'Data too old');
        return null;
      }
      
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('Decryption failed:', error);
      this.logSecurityEvent('DECRYPTION_FAILED', error.message);
      return null;
    }
  }

  xorEncrypt(text, key) {
    let result = '';
    for (let i = 0; i < text.length; i++) {
      const keyChar = key.charCodeAt(i % key.length);
      result += String.fromCharCode(text.charCodeAt(i) ^ keyChar);
    }
    return result;
  }

  xorDecrypt(encryptedText, key) {
    return this.xorEncrypt(encryptedText, key); // XOR is symmetric
  }

  // ===== Secure Storage =====
  secureStore(key, data) {
    if (!this.validateData(data)) {
      this.logSecurityEvent('INVALID_DATA_STORE', key);
      return false;
    }

    const encrypted = this.advancedEncrypt(data);
    if (encrypted) {
      const secureKey = `hammad_secure_${this.hashKey(key)}`;
      localStorage.setItem(secureKey, encrypted);
      this.updateActivity();
      return true;
    }
    return false;
  }

  secureRetrieve(key) {
    const secureKey = `hammad_secure_${this.hashKey(key)}`;
    const encrypted = localStorage.getItem(secureKey);
    
    if (encrypted) {
      this.updateActivity();
      const decrypted = this.advancedDecrypt(encrypted);
      
      if (decrypted && this.validateData(decrypted)) {
        return decrypted;
      } else {
        // Remove corrupted data
        localStorage.removeItem(secureKey);
        this.logSecurityEvent('CORRUPTED_DATA_REMOVED', key);
      }
    }
    return null;
  }

  hashKey(key) {
    let hash = 0;
    for (let i = 0; i < key.length; i++) {
      const char = key.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  }

  // ===== Input Validation & Sanitization =====
  validateInput(input) {
    if (typeof input !== 'string') return false;
    
    // Check for XSS patterns
    const xssPatterns = [
      /<script[\s\S]*?>[\s\S]*?<\/script>/gi,
      /<iframe[\s\S]*?>[\s\S]*?<\/iframe>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<object[\s\S]*?>[\s\S]*?<\/object>/gi,
      /<embed[\s\S]*?>/gi,
      /eval\s*\(/gi,
      /expression\s*\(/gi
    ];

    for (const pattern of xssPatterns) {
      if (pattern.test(input)) {
        this.logSecurityEvent('XSS_ATTEMPT', input.substring(0, 100));
        this.failedAttempts++;
        return false;
      }
    }

    // Check for SQL injection patterns
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
      /('|(\\')|(;)|(\\))/gi
    ];

    for (const pattern of sqlPatterns) {
      if (pattern.test(input)) {
        this.logSecurityEvent('SQL_INJECTION_ATTEMPT', input.substring(0, 100));
        this.failedAttempts++;
        return false;
      }
    }

    return true;
  }

  sanitizeInput(input) {
    if (typeof input !== 'string') return '';
    
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
      .replace(/\\/g, '&#x5C;')
      .replace(/`/g, '&#x60;');
  }

  validateData(data) {
    try {
      JSON.stringify(data);
      return true;
    } catch {
      return false;
    }
  }

  // ===== Session Management =====
  updateActivity() {
    this.lastActivity = Date.now();
    this.secureStore('lastActivity', this.lastActivity);
  }

  checkSession() {
    const now = Date.now();
    const timeSinceActivity = now - this.lastActivity;
    
    if (timeSinceActivity > this.maxInactivity) {
      this.logSecurityEvent('SESSION_EXPIRED', `Inactive for ${timeSinceActivity}ms`);
      this.clearAllSecureData();
      this.showSecurityAlert('Session expired for security reasons', 'warning');
      return false;
    }
    
    return true;
  }

  // ===== Attack Detection =====
  detectSuspiciousActivity() {
    if (this.failedAttempts >= this.maxFailedAttempts) {
      this.logSecurityEvent('MULTIPLE_FAILED_ATTEMPTS', `${this.failedAttempts} attempts`);
      this.blockCurrentSession();
      return true;
    }
    return false;
  }

  blockCurrentSession() {
    this.clearAllSecureData();
    this.showSecurityAlert('Multiple security violations detected. Access blocked.', 'error');
    
    // Disable all inputs
    const inputs = document.querySelectorAll('input, textarea, button');
    inputs.forEach(input => input.disabled = true);
    
    // Redirect after delay
    setTimeout(() => {
      window.location.reload();
    }, 3000);
  }

  // ===== Security Monitoring =====
  logSecurityEvent(type, details) {
    const event = {
      type,
      details,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    this.suspiciousActivity.push(event);
    
    // Keep only last 100 events
    if (this.suspiciousActivity.length > 100) {
      this.suspiciousActivity = this.suspiciousActivity.slice(-100);
    }
    
    console.warn(`🚨 Security Event: ${type}`, details);
  }

  // ===== Data Protection =====
  clearAllSecureData() {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith('hammad_secure_')) {
        localStorage.removeItem(key);
      }
    });
    
    // Clear session storage too
    sessionStorage.clear();
  }

  // ===== Security Alerts =====
  showSecurityAlert(message, type = 'warning') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `security-alert security-alert-${type}`;
    alertDiv.innerHTML = `
      <div class="security-alert-content">
        <i class="fas fa-shield-alt"></i>
        <span>${message}</span>
        <button onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
      if (alertDiv.parentElement) {
        alertDiv.remove();
      }
    }, 5000);
  }

  // ===== Initialization =====
  initAdvancedSecurity() {
    // Session monitoring
    setInterval(() => {
      if (!this.checkSession()) return;
      this.detectSuspiciousActivity();
    }, 30000); // Check every 30 seconds

    // Activity monitoring
    ['click', 'keypress', 'mousemove', 'scroll'].forEach(event => {
      document.addEventListener(event, () => this.updateActivity(), { passive: true });
    });

    // Page visibility monitoring
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.updateActivity();
      }
    });

    // Prevent right-click in production
    if (window.location.hostname !== 'localhost') {
      document.addEventListener('contextmenu', (e) => {
        e.preventDefault();
        this.logSecurityEvent('RIGHT_CLICK_BLOCKED', 'Context menu blocked');
      });
    }

    // Prevent F12 and other dev tools shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.key === 'F12' || 
          (e.ctrlKey && e.shiftKey && e.key === 'I') ||
          (e.ctrlKey && e.shiftKey && e.key === 'C') ||
          (e.ctrlKey && e.key === 'U')) {
        e.preventDefault();
        this.logSecurityEvent('DEV_TOOLS_BLOCKED', `Key: ${e.key}`);
      }
    });

    // Monitor for console access
    let devtools = { open: false, orientation: null };
    const threshold = 160;
    
    setInterval(() => {
      if (window.outerHeight - window.innerHeight > threshold || 
          window.outerWidth - window.innerWidth > threshold) {
        if (!devtools.open) {
          devtools.open = true;
          this.logSecurityEvent('DEV_TOOLS_DETECTED', 'Developer tools opened');
        }
      } else {
        devtools.open = false;
      }
    }, 500);

    console.log('🛡️ HAMMAD BHAI Advanced Security System Activated');
    this.showSecurityAlert('Advanced security protection enabled', 'success');
  }
}

// Initialize Advanced Security
window.hammadSecurity = new AdvancedSecurity();
