<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>HAMMAD BHAI - Ultimate AI Assistant</title>

    <!-- Creator & Copyright Information -->
    <meta name="author" content="<PERSON><PERSON><PERSON><PERSON><PERSON> HAMMAD ZUBAIR" />
    <meta name="creator" content="MUHAMMAD HAMMAD ZUBAIR" />
    <meta name="developer" content="MUHAMMAD HAMMAD ZUBAIR" />
    <meta
      name="description"
      content="HAMMAD BHAI - The Ultimate AI Assistant with 87.6% World Knowledge Coverage. Created by <PERSON><PERSON><PERSON><PERSON><PERSON> HAMMAD ZUBAIR"
    />

    <!-- Favicon -->
    <link
      rel="icon"
      type="image/x-icon"
      href="{{ url_for('static', filename='favicon.ico') }}"
    />

    <!-- External Libraries -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
    />

    <!-- Custom Styles -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}?v=2025"
    />
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loadingScreen" class="loading-screen">
      <div class="loading-content">
        <div class="loading-logo">
          <i class="fas fa-robot"></i>
        </div>
        <div class="loading-text">
          <h2>HAMMAD BHAI</h2>
          <p>Ultimate AI Assistant</p>
          <div class="loading-bar">
            <div class="loading-progress"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Authentication Modal -->
    <div id="authModal" class="auth-modal">
      <div class="auth-content">
        <div class="auth-header">
          <h2><i class="fas fa-robot"></i> HAMMAD BHAI</h2>
          <p>Ultimate AI Assistant with 87.6% World Knowledge</p>
        </div>

        <div class="auth-tabs">
          <button class="auth-tab active" data-tab="login">Login</button>
          <button class="auth-tab" data-tab="register">Register</button>
          <button class="auth-tab" data-tab="guest">Guest Mode</button>
        </div>

        <div class="auth-forms">
          <!-- Login Form -->
          <div id="loginForm" class="auth-form active">
            <div class="form-group">
              <i class="fas fa-user"></i>
              <input
                type="text"
                id="loginUsername"
                placeholder="Username or Email"
                required
              />
            </div>
            <div class="form-group">
              <i class="fas fa-lock"></i>
              <input
                type="password"
                id="loginPassword"
                placeholder="Password"
                required
              />
              <button
                type="button"
                class="password-toggle"
                onclick="togglePassword('loginPassword')"
              >
                <i class="fas fa-eye"></i>
              </button>
            </div>
            <button class="auth-btn" onclick="handleLogin()">
              <i class="fas fa-sign-in-alt"></i> Login
            </button>
            <div class="auth-links">
              <a href="#" onclick="showForgotPassword()">Forgot Password?</a>
            </div>
          </div>

          <!-- Register Form -->
          <div id="registerForm" class="auth-form">
            <div class="form-group">
              <i class="fas fa-user"></i>
              <input
                type="text"
                id="registerUsername"
                placeholder="Username"
                required
              />
            </div>
            <div class="form-group">
              <i class="fas fa-envelope"></i>
              <input
                type="email"
                id="registerEmail"
                placeholder="Email"
                required
              />
            </div>
            <div class="form-group">
              <i class="fas fa-lock"></i>
              <input
                type="password"
                id="registerPassword"
                placeholder="Password"
                required
              />
              <button
                type="button"
                class="password-toggle"
                onclick="togglePassword('registerPassword')"
              >
                <i class="fas fa-eye"></i>
              </button>
            </div>
            <div class="form-group">
              <i class="fas fa-lock"></i>
              <input
                type="password"
                id="confirmPassword"
                placeholder="Confirm Password"
                required
              />
              <button
                type="button"
                class="password-toggle"
                onclick="togglePassword('confirmPassword')"
              >
                <i class="fas fa-eye"></i>
              </button>
            </div>
            <button class="auth-btn" onclick="handleRegister()">
              <i class="fas fa-user-plus"></i> Create Account
            </button>
          </div>

          <!-- Guest Mode -->
          <div id="guestForm" class="auth-form">
            <div class="guest-info">
              <i class="fas fa-info-circle"></i>
              <h3>Guest Mode</h3>
              <p>Continue without account. Limited features available.</p>
              <ul>
                <li><i class="fas fa-check"></i> Full AI Chat Access</li>
                <li><i class="fas fa-check"></i> Real-time Information</li>
                <li>
                  <i class="fas fa-times"></i> Chat History (Session Only)
                </li>
                <li><i class="fas fa-times"></i> Personalized Settings</li>
              </ul>
            </div>
            <button class="auth-btn guest-btn" onclick="enterGuestMode()">
              <i class="fas fa-user"></i> Continue as Guest
            </button>
          </div>
        </div>

        <div class="auth-footer">
          <p>Created with ❤️ by <strong>MUHAMMAD HAMMAD ZUBAIR</strong></p>
          <div class="auth-stats">
            <span><i class="fas fa-globe"></i> 87.6% World Knowledge</span>
            <span><i class="fas fa-api"></i> 25+ APIs</span>
            <span><i class="fas fa-dollar-sign"></i> 100% FREE</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" class="main-app hidden">
      <!-- Sidebar -->
      <div id="sidebar" class="sidebar">
        <!-- Sidebar Header -->
        <div class="sidebar-header">
          <div class="logo">
            <i class="fas fa-robot"></i>
            <span class="logo-text">HAMMAD BHAI</span>
          </div>
          <button class="sidebar-toggle" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
          </button>
        </div>

        <!-- New Chat Button -->
        <div class="new-chat-section">
          <button class="new-chat-btn" onclick="startNewChat()">
            <i class="fas fa-plus"></i>
            <span>New Chat</span>
          </button>
        </div>

        <!-- Search Chats -->
        <div class="search-section">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input
              type="text"
              id="chatSearch"
              placeholder="Search chats..."
              oninput="searchChats()"
            />
          </div>
        </div>

        <!-- Chat History -->
        <div class="chat-history">
          <div class="history-header">
            <h3>Chat History</h3>
            <div class="history-actions">
              <button
                class="action-btn"
                onclick="exportChats()"
                title="Export Chats"
              >
                <i class="fas fa-download"></i>
              </button>
              <button
                class="action-btn"
                onclick="deleteAllChats()"
                title="Delete All Chats"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>

          <!-- Today -->
          <div class="history-group">
            <div class="group-header">
              <span>Today</span>
            </div>
            <div id="todayChats" class="chat-list">
              <!-- Chat items will be populated here -->
            </div>
          </div>

          <!-- Yesterday -->
          <div class="history-group">
            <div class="group-header">
              <span>Yesterday</span>
            </div>
            <div id="yesterdayChats" class="chat-list">
              <!-- Chat items will be populated here -->
            </div>
          </div>

          <!-- Previous 7 Days -->
          <div class="history-group">
            <div class="group-header">
              <span>Previous 7 Days</span>
            </div>
            <div id="weekChats" class="chat-list">
              <!-- Chat items will be populated here -->
            </div>
          </div>

          <!-- Older -->
          <div class="history-group">
            <div class="group-header">
              <span>Older</span>
            </div>
            <div id="olderChats" class="chat-list">
              <!-- Chat items will be populated here -->
            </div>
          </div>
        </div>

        <!-- Sidebar Footer -->
        <div class="sidebar-footer">
          <!-- User Profile -->
          <div class="user-profile" onclick="toggleUserMenu()">
            <div class="user-avatar">
              <i class="fas fa-user"></i>
            </div>
            <div class="user-info">
              <span class="username" id="currentUsername">Guest User</span>
              <span class="user-status">Online</span>
            </div>
            <i class="fas fa-chevron-up"></i>
          </div>

          <!-- User Menu -->
          <div id="userMenu" class="user-menu hidden">
            <a href="#" onclick="showSettings()">
              <i class="fas fa-cog"></i> Settings
            </a>
            <a href="#" onclick="showProfile()">
              <i class="fas fa-user-edit"></i> Profile
            </a>
            <a href="#" onclick="showHelp()">
              <i class="fas fa-question-circle"></i> Help
            </a>
            <a href="#" onclick="logout()">
              <i class="fas fa-sign-out-alt"></i> Logout
            </a>
          </div>

          <!-- App Info -->
          <div class="app-info">
            <div class="info-stats">
              <span><i class="fas fa-database"></i> 87.6% Knowledge</span>
              <span><i class="fas fa-bolt"></i> 25+ APIs</span>
            </div>
            <div class="creator-info">
              <span>Created by <strong>MUHAMMAD HAMMAD ZUBAIR</strong></span>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="main-content">
        <!-- Top Header -->
        <div class="top-header">
          <div class="header-left">
            <button class="mobile-menu-btn" onclick="toggleSidebar()">
              <i class="fas fa-bars"></i>
            </button>
            <div class="chat-title">
              <h2 id="currentChatTitle">HAMMAD BHAI</h2>
              <span id="chatSubtitle">Ultimate AI Assistant</span>
            </div>
          </div>

          <div class="header-right">
            <!-- AI Model Selector -->
            <div class="model-selector">
              <select id="aiModel" onchange="switchModel()">
                <option value="gemini-2.5-flash-preview-05-20">
                  Gemini 2.5 Flash (Most Powerful)
                </option>
                <option value="gemini-2.0-flash-exp">
                  Gemini 2.0 Experimental
                </option>
                <option value="gemini-2.0-flash">Gemini 2.0 Flash</option>
                <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                <option value="gemini-pro">Gemini Pro</option>
              </select>
            </div>

            <!-- Theme Toggle -->
            <button
              class="theme-toggle"
              onclick="toggleTheme()"
              title="Toggle Theme"
            >
              <i class="fas fa-moon"></i>
            </button>

            <!-- Settings -->
            <button
              class="settings-btn"
              onclick="showSettings()"
              title="Settings"
            >
              <i class="fas fa-cog"></i>
            </button>
          </div>
        </div>

        <!-- Chat Container -->
        <div class="chat-container">
          <!-- Welcome Screen -->
          <div id="welcomeScreen" class="welcome-screen">
            <div class="welcome-content">
              <div class="welcome-logo">
                <i class="fas fa-robot"></i>
              </div>
              <h1>HAMMAD BHAI AI CHAT</h1>
              <p>
                Next-Generation AI Assistant with Maximum Security, Advanced
                Features & 87.6% World Knowledge Coverage
              </p>

              <!-- Quick Actions -->
              <div class="quick-actions">
                <button
                  class="quick-action"
                  onclick="sendQuickMessage('What is artificial intelligence?')"
                >
                  <i class="fas fa-brain"></i>
                  <span>Explain AI</span>
                </button>
                <button
                  class="quick-action"
                  onclick="sendQuickMessage('What is the weather today?')"
                >
                  <i class="fas fa-cloud-sun"></i>
                  <span>Weather</span>
                </button>
                <button
                  class="quick-action"
                  onclick="sendQuickMessage('Tell me latest news')"
                >
                  <i class="fas fa-newspaper"></i>
                  <span>Latest News</span>
                </button>
                <button
                  class="quick-action"
                  onclick="sendQuickMessage('What time is it?')"
                >
                  <i class="fas fa-clock"></i>
                  <span>Current Time</span>
                </button>
              </div>

              <!-- Features -->
              <div class="features-grid">
                <div class="feature-card">
                  <i class="fas fa-globe"></i>
                  <h3>87.6% World Knowledge</h3>
                  <p>Comprehensive information from 25+ sources</p>
                </div>
                <div class="feature-card">
                  <i class="fas fa-bolt"></i>
                  <h3>Real-time Data</h3>
                  <p>Live weather, news, finance, and more</p>
                </div>
                <div class="feature-card">
                  <i class="fas fa-language"></i>
                  <h3>50+ Languages</h3>
                  <p>Communicate in your preferred language</p>
                </div>
                <div class="feature-card">
                  <i class="fas fa-shield-alt"></i>
                  <h3>Maximum Security</h3>
                  <p>Military-grade encryption & advanced protection</p>
                </div>
                <div class="feature-card">
                  <i class="fas fa-rocket"></i>
                  <h3>ChatGPT Killer</h3>
                  <p>Better features, faster responses, 100% free</p>
                </div>
                <div class="feature-card">
                  <i class="fas fa-mobile-alt"></i>
                  <h3>Fully Responsive</h3>
                  <p>Perfect experience on all devices</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Chat Messages -->
          <div id="chatMessages" class="chat-messages hidden">
            <!-- Messages will be populated here -->
          </div>
        </div>

        <!-- Input Section -->
        <div class="input-section">
          <div class="input-container">
            <div class="input-wrapper">
              <button
                class="attachment-btn"
                onclick="showAttachmentMenu()"
                title="Attach File"
              >
                <i class="fas fa-paperclip"></i>
              </button>

              <textarea
                id="messageInput"
                placeholder="Ask anything..."
                rows="1"
                onkeydown="handleKeyDown(event)"
                oninput="autoResize(this)"
              ></textarea>

              <div class="input-actions">
                <button
                  class="voice-btn"
                  onclick="toggleVoiceInput()"
                  title="Voice Input"
                >
                  <i class="fas fa-microphone"></i>
                </button>
                <button
                  id="sendBtn"
                  class="send-btn"
                  onclick="sendMessage()"
                  disabled
                >
                  <i class="fas fa-paper-plane"></i>
                </button>
              </div>
            </div>

            <!-- Input Footer -->
            <div class="input-footer">
              <div class="input-info">
                <span
                  >HAMMAD BHAI can make mistakes. Consider checking important
                  information.</span
                >
              </div>
              <div class="input-stats">
                <span id="charCount">0</span>
                <span>characters</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Advanced Security Indicator -->
    <div
      class="security-indicator"
      onclick="showSecurityStatus()"
      title="Click to view security status"
    >
      <i class="fas fa-shield-alt"></i>
      <span>Maximum Security</span>
    </div>

    <!-- Advanced Security System -->
    <script src="{{ url_for('static', filename='js/security.js') }}?v=2025"></script>

    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/script.js') }}?v=2025"></script>
  </body>
</html>
